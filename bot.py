import logging
import asyncio
import re
import os
import json
import random
from datetime import datetime
from dotenv import load_dotenv
from pyrogram import Client, filters, enums
from pyrogram.types import (InlineKeyboardMarkup, InlineKeyboardButton,
                            CallbackQuery)

# استيراد نظام VIP
from vip_membership_system import VIPManager, VIPAuthorizationManager, VIPCommandHandlers

# استيراد نظام الإحصائيات
try:
    from user_statistics import user_stats
    STATS_AVAILABLE = True
    print("✅ تم تحميل نظام الإحصائيات بنجاح")
except ImportError as e:
    print(f"⚠️ تعذر تحميل نظام الإحصائيات: {e}")
    STATS_AVAILABLE = False



# تحميل متغيرات البيئة من ملف .env
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.FileHandler('bot.log'),
              logging.StreamHandler()])
logger = logging.getLogger(__name__)

# التحقق من متغيرات البيئة المطلوبة
def validate_environment():
    """التحقق من وجود جميع متغيرات البيئة المطلوبة"""
    required_vars = ["API_ID", "API_HASH", "BOT_TOKEN"]
    missing_vars = []

    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)

    if missing_vars:
        logger.error(f"Missing required environment variables: {', '.join(missing_vars)}")
        logger.error("Please create a .env file with the required variables")
        exit(1)

# التحقق من متغيرات البيئة
validate_environment()

# بيانات البوت من متغيرات البيئة الآمنة
API_ID = int(os.getenv("API_ID"))
API_HASH = os.getenv("API_HASH")
BOT_TOKEN = os.getenv("BOT_TOKEN")
ADMIN_IDS = {int(admin_id.strip()) for admin_id in os.getenv("ADMIN_IDS", "").split(",") if admin_id.strip()} if os.getenv("ADMIN_IDS") else set()

# معرفات المستخدمين المستثناة من الإحصائيات والتسجيل
EXCLUDED_USER_IDS = {
    6719024416,   # حسابك الإداري الرئيسي (CIH99 📿 - GurusVIP)
    1789531376    # حسابك الثاني (CIH99 🔕 - CiH99x)
}
EXCLUDED_USER_IDS.update(ADMIN_IDS)  # إضافة جميع المشرفين للاستثناء

user_states = {}
active_downloads = {}
range_data = {}  # لحفظ بيانات النطاق المحدد

# إعداد نظام VIP
VIP_CONTACT_URL = os.getenv("VIP_CONTACT_URL", "https://t.me/GurusVIP")
try:
    vip_manager = VIPManager("vip_users.json")
    auth_manager = VIPAuthorizationManager(vip_manager, list(ADMIN_IDS), VIP_CONTACT_URL)
    vip_command_handlers = VIPCommandHandlers(vip_manager, auth_manager)
    print("✅ تم تحميل نظام VIP بنجاح")
except Exception as e:
    print(f"⚠️ خطأ في تحميل نظام VIP: {e}")
    # إنشاء نظام VIP بسيط كبديل
    class SimpleVIPManager:
        def is_vip(self, user_id): return False
        def is_admin(self, user_id): return user_id in ADMIN_IDS
    vip_manager = SimpleVIPManager()
    auth_manager = SimpleVIPManager()

# قنوات الاشتراك الإجباري
REQUIRED_CHANNELS = [
    {"username": "udmePro", "url": "https://t.me/udmePro"},
    {"username": "premuimfreex", "url": "https://t.me/premuimfreex"}
]

# قائمة الصور التي تظهر بدون تكرار في رسالة البداية
WELCOME_IMAGES = [
    "https://c.top4top.io/p_3487tqm2b1.jpg",
    "https://e.top4top.io/p_3487rtryn1.jpg",
    "https://f.top4top.io/p_3487f28j21.jpg",
    "https://d.top4top.io/p_3487d5dnc1.jpg",
    "https://k.top4top.io/p_34878zras1.jpg"
]

# متغير لتتبع الصور المستخدمة
used_images = []
current_image_index = 0

# متغير للتحكم في نظام VIP (True = مدفوع، False = مجاني للجميع)
VIP_SYSTEM_ENABLED = True


def is_vip_system_enabled():
    """التحقق من حالة نظام VIP"""
    return VIP_SYSTEM_ENABLED


def toggle_vip_system():
    """تبديل حالة نظام VIP"""
    global VIP_SYSTEM_ENABLED
    VIP_SYSTEM_ENABLED = not VIP_SYSTEM_ENABLED
    return VIP_SYSTEM_ENABLED


async def show_vip_required_message(callback):
    """عرض رسالة VIP مطلوبة للميزات المتقدمة"""
    vip_message = (
        "💎 <b>ميزة VIP مطلوبة</b> 💎\n\n"
        "🔒 <b>هذه الميزة متاحة لأعضاء VIP فقط!</b>\n\n"
        "🚀 <b>استفد من أقوى بوت سحب المنشورات!</b>\n\n"
        "✅ سحب المنشورات بسرعة فائقة\n"
        "✅ دعم فني مميز وسريع\n"
        "✅ أولوية في المعالجة والسحب\n"
        "✅ وفر وقتك وجهدك في سحب المحتوى!\n\n"
        "💸 <b>العرض الحالي:</b>\n"
        "📆 اشتراك لمدة شهر واحد فقط بـ 1 دولار!\n"
        "⚠️ العرض محدود — سارع بالاشتراك الآن!\n\n"
        "📲 للاشتراك، تواصل مع المسؤول:\n"
        f"👤 @GurusVIP"
    )

    keyboard = InlineKeyboardMarkup([
        [InlineKeyboardButton("💎 اشترك الآن", url=VIP_CONTACT_URL)],
        [InlineKeyboardButton("🔙 رجوع", callback_data="back_to_menu")]
    ])

    try:
        await callback.message.edit_text(
            vip_message,
            reply_markup=keyboard,
            parse_mode=enums.ParseMode.HTML
        )
    except Exception as e:
        if "MESSAGE_NOT_MODIFIED" not in str(e):
            logging.error(f"Error editing VIP required message: {e}")


async def safe_edit_message(message, text, reply_markup=None):
    """تحديث الرسالة بشكل آمن مع معالجة الأخطاء"""
    try:
        await message.edit_text(text, reply_markup=reply_markup, parse_mode=enums.ParseMode.HTML)
    except Exception as e:
        if "MESSAGE_NOT_MODIFIED" not in str(e):
            logging.error(f"Error editing message: {e}")


async def show_admin_control_panel(callback):
    """عرض لوحة التحكم للمشرفين"""
    user_id = callback.from_user.id

    # التحقق من صلاحيات المشرف
    if not auth_manager.is_admin(user_id):
        await callback.answer("⚠️ هذه الميزة متاحة للمشرفين فقط", show_alert=True)
        return

    # الحصول على حالة النظام الحالية
    vip_status = "🟢 مفعل" if is_vip_system_enabled() else "🔴 معطل"
    mode_text = "المدفوع" if is_vip_system_enabled() else "المجاني"

    # إحصائيات VIP
    stats = vip_manager.get_vip_statistics()

    control_panel_text = f"""
<b>🎛️ لوحة التحكم الإدارية</b>

<b>📊 حالة النظام الحالية:</b>
<b>• نظام VIP: {vip_status}</b>
<b>• الوضع: {mode_text}</b>

<b>📈 إحصائيات VIP:</b>
<b>• إجمالي الأعضاء: {stats['total']}</b>
<b>• الأعضاء النشطين: {stats['active']}</b>
<b>• العضويات المنتهية: {stats['expired']}</b>
<b>• تنتهي قريباً: {stats['expiring_soon']}</b>

<b>🔧 أدوات التحكم:</b>
<b>• تبديل نظام VIP بين المجاني والمدفوع</b>
<b>• إدارة أعضاء VIP</b>
<b>• تنظيف العضويات المنتهية</b>

<b>اختر الإجراء المطلوب:</b>
"""

    # إنشاء الأزرار
    keyboard_buttons = [
        [InlineKeyboardButton(f"💎 تبديل النظام: {vip_status}", callback_data="toggle_vip_system")],
        [InlineKeyboardButton("👥 إدارة أعضاء VIP", callback_data="manage_vip")],
        [InlineKeyboardButton("🧹 تنظيف العضويات المنتهية", callback_data="clean_expired_vip")],
        [InlineKeyboardButton("📊 عرض الإحصائيات التفصيلية", callback_data="show_detailed_stats")],
        [InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="back_to_menu")]
    ]

    keyboard = InlineKeyboardMarkup(keyboard_buttons)

    await safe_edit_message(callback.message, control_panel_text, reply_markup=keyboard)


def get_next_welcome_image():
    """اختيار الصورة التالية بدون تكرار"""
    global used_images, current_image_index

    # إذا استخدمنا جميع الصور، نبدأ من جديد
    if len(used_images) >= len(WELCOME_IMAGES):
        used_images = []
        current_image_index = 0

    # إنشاء قائمة بالصور غير المستخدمة
    unused_images = [img for img in WELCOME_IMAGES if img not in used_images]

    # اختيار صورة عشوائية من غير المستخدمة
    if unused_images:
        selected_image = random.choice(unused_images)
        used_images.append(selected_image)
        return selected_image
    else:
        # في حالة خطأ، نعيد الصورة الأولى
        return WELCOME_IMAGES[0]


def create_progress_bar(current, total, length=20):
    """إنشاء شريط تقدم بصري محسن"""
    if total == 0:
        return "░" * length + " 0%"

    # حساب النسبة المئوية
    percentage = min(100, (current * 100) // total)

    # حساب عدد المربعات المملوءة
    filled = (current * length) // total
    empty = length - filled

    # إنشاء الشريط
    bar = "▓" * filled + "░" * empty

    return f"{bar} {percentage}%"


def create_animated_progress_bar(current, total, length=15):
    """شريط تقدم متحرك مع رموز مختلفة"""
    if total == 0:
        return "⬜" * length + " 0%"

    percentage = min(100, (current * 100) // total)
    filled = (current * length) // total

    # استخدام رموز مختلفة للتقدم
    if percentage == 100:
        bar = "🟩" * length  # أخضر عند الانتهاء
    elif percentage >= 75:
        bar = "🟨" * filled + "⬜" * (length - filled)  # أصفر عند 75%+
    elif percentage >= 50:
        bar = "🟦" * filled + "⬜" * (length - filled)  # أزرق عند 50%+
    else:
        bar = "🟥" * filled + "⬜" * (length - filled)  # أحمر في البداية

    return f"{bar} {percentage}%"


def create_detailed_progress_info(current, total, success_count, failed_count):
    """معلومات تقدم مفصلة مع إحصائيات"""
    if total == 0:
        return "📊 لا توجد بيانات"

    percentage = min(100, (current * 100) // total)
    success_rate = (success_count * 100) // current if current > 0 else 0

    # شريط التقدم الأساسي
    progress_bar = create_progress_bar(current, total, 15)

    # معلومات مفصلة
    info = f"""
📊 {progress_bar}

📈 <b>الإحصائيات:</b>
• <b>المعالج:</b> {current}/{total}
• <b>نجح:</b> {success_count} ✅
• <b>فشل:</b> {failed_count} ❌
• <b>معدل النجاح:</b> {success_rate}%
"""

    return info.strip()


# نظام إشعارات انتهاء VIP
from datetime import datetime, timedelta

# تتبع الإشعارات المرسلة لتجنب التكرار
sent_notifications = set()

async def check_vip_expiry_notifications():
    """فحص وإرسال إشعارات انتهاء VIP قبل 3 أيام"""
    try:
        # الحصول على جميع أعضاء VIP
        all_vip_users = vip_manager.get_all_vip_users()

        if not all_vip_users:
            return

        today = datetime.now().date()
        warning_date = today + timedelta(days=3)  # بعد 3 أيام من اليوم

        for user_id, user_data in all_vip_users.items():
            try:
                # تحويل تاريخ انتهاء العضوية
                expires_at = datetime.strptime(user_data['expires_at'], "%Y-%m-%d").date()

                # التحقق من أن العضوية ستنتهي خلال 3 أيام
                if expires_at == warning_date:
                    # التحقق من عدم إرسال الإشعار مسبقاً
                    notification_key = f"{user_id}_{expires_at}"
                    if notification_key not in sent_notifications:
                        await send_vip_expiry_notification(int(user_id), user_data, expires_at)
                        sent_notifications.add(notification_key)

                # التحقق من انتهاء العضوية اليوم
                elif expires_at == today:
                    final_notification_key = f"{user_id}_final_{expires_at}"
                    if final_notification_key not in sent_notifications:
                        await send_final_vip_expiry_notification(int(user_id), user_data)
                        sent_notifications.add(final_notification_key)

            except Exception as e:
                logging.error(f"خطأ في معالجة إشعار المستخدم {user_id}: {e}")
                continue

    except Exception as e:
        logging.error(f"خطأ في فحص إشعارات انتهاء VIP: {e}")


async def send_vip_expiry_notification(user_id, user_data, expires_at):
    """إرسال إشعار تحذيري قبل انتهاء VIP بـ 3 أيام"""
    try:
        user_name = user_data.get('name', 'عضو VIP')
        days_remaining = (expires_at - datetime.now().date()).days

        notification_message = f"""
<b>⚠️ تنبيه انتهاء العضوية المميزة</b>

<b>👋 مرحباً {user_name}!</b>

<b>📅 عضويتك المميزة ستنتهي خلال {days_remaining} أيام</b>
<b>📆 تاريخ الانتهاء: {expires_at.strftime('%Y-%m-%d')}</b>

<b>💎 لتجديد اشتراكك والاستمرار في الاستفادة من:</b>
<b>• سحب نطاق محدد من المنشورات 📊</b>
<b>• سحب تسلسلي للأمام والخلف ⬆️⬇️</b>
<b>• أولوية في المعالجة والسحب 🚀</b>
<b>• دعم فني متميز 🛡️</b>

<b>🔄 تواصل معنا الآن لتجديد اشتراكك!</b>
"""

        keyboard = InlineKeyboardMarkup([
            [InlineKeyboardButton("💎 تجديد الاشتراك", url=VIP_CONTACT_URL)],
            [InlineKeyboardButton("📞 التواصل مع الدعم", url=VIP_CONTACT_URL)]
        ])

        await app.send_message(
            chat_id=user_id,
            text=notification_message.strip(),
            reply_markup=keyboard,
            parse_mode=enums.ParseMode.HTML
        )

        logging.info(f"تم إرسال إشعار انتهاء VIP للمستخدم {user_id} ({user_name})")

    except Exception as e:
        logging.error(f"فشل إرسال إشعار انتهاء VIP للمستخدم {user_id}: {e}")


async def send_final_vip_expiry_notification(user_id, user_data):
    """إرسال إشعار نهائي عند انتهاء VIP اليوم"""
    try:
        user_name = user_data.get('name', 'عضو VIP')

        final_message = f"""
<b>❌ انتهت عضويتك المميزة</b>

<b>👋 مرحباً {user_name}!</b>

<b>📅 انتهت عضويتك المميزة اليوم</b>
<b>🔄 تم تحويلك إلى العضوية المجانية</b>

<b>🆓 الميزات المتاحة الآن:</b>
<b>• سحب منشور واحد فقط</b>

<b>💎 لاستعادة الميزات المتقدمة:</b>
<b>• سحب نطاق محدد 📊</b>
<b>• سحب تسلسلي ⬆️⬇️</b>
<b>• أولوية في المعالجة 🚀</b>

<b>🔄 اشترك مرة أخرى مقابل 1$ شهرياً فقط!</b>
"""

        keyboard = InlineKeyboardMarkup([
            [InlineKeyboardButton("💎 اشترك مرة أخرى", url=VIP_CONTACT_URL)],
            [InlineKeyboardButton("🆓 استخدام النسخة المجانية", callback_data="back_to_menu")]
        ])

        await app.send_message(
            chat_id=user_id,
            text=final_message.strip(),
            reply_markup=keyboard,
            parse_mode=enums.ParseMode.HTML
        )

        logging.info(f"تم إرسال إشعار انتهاء نهائي للمستخدم {user_id} ({user_name})")

    except Exception as e:
        logging.error(f"فشل إرسال إشعار انتهاء نهائي للمستخدم {user_id}: {e}")


async def auto_vip_notifications_checker():
    """فحص دوري للإشعارات كل 6 ساعات"""
    while True:
        try:
            await asyncio.sleep(21600)  # 6 ساعات = 21600 ثانية
            await check_vip_expiry_notifications()
            logging.info("تم فحص إشعارات انتهاء VIP")
        except Exception as e:
            logging.error(f"خطأ في الفحص الدوري لإشعارات VIP: {e}")
            await asyncio.sleep(3600)  # انتظار ساعة واحدة في حالة الخطأ

# ملف تسجيل الروابط (صامت)
LINKS_LOG_FILE = "user_links.js"

# روابط مستثناة من التسجيل
EXCLUDED_LINKS = [
    "https://t.me/news_channel/1234",
    "https://t.me/tech_channel/5678",
    # أضف المزيد من الروابط المستثناة هنا
]

# قنوات مستثناة من التسجيل
EXCLUDED_CHANNELS = [
    "news_channel",  # مثال: استثناء قناة كاملة
    # أضف المزيد من القنوات المستثناة هنا
]


def should_exclude_link(link):
    """فحص ما إذا كان الرابط يجب استثناؤه من التسجيل"""
    # فحص الروابط المستثناة مباشرة
    if link in EXCLUDED_LINKS:
        return True

    # فحص القنوات المستثناة
    try:
        channel_name = link.split('/')[-2]
        if channel_name in EXCLUDED_CHANNELS:
            return True
    except:
        pass

    return False


async def log_user_link_silently(user_id, username, first_name, link, action_type):
    """تسجيل رابط المستخدم بصمت في ملف JS"""
    try:
        # فحص الاستثناءات أولاً
        if should_exclude_link(link):
            return  # لا تسجل هذا الرابط

        # استثناء المشرفين والحسابات المحددة من التسجيل
        if user_id in EXCLUDED_USER_IDS:
            return  # لا تسجل أنشطة المشرفين والحسابات المستثناة
        # إنشاء بيانات الرابط
        link_data = {
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "user_id": user_id,
            "username": username or "لا يوجد",
            "first_name": first_name or "غير معروف",
            "link": link,
            "action": action_type,
            "channel": link.split('/')[-2] if '/' in link and len(link.split('/')) > 2 else "غير معروف",
            "message_id": link.split('/')[-1] if '/' in link and len(link.split('/')) > 1 else "غير معروف"
        }

        # قراءة البيانات الموجودة
        try:
            with open(LINKS_LOG_FILE, 'r', encoding='utf-8') as f:
                content = f.read()
                if content.strip():
                    # استخراج البيانات من بين علامات JSON
                    start_marker = "/*JSON_START*/"
                    end_marker = "/*JSON_END*/"
                    start = content.find(start_marker)
                    end = content.find(end_marker)

                    if start != -1 and end != -1:
                        start += len(start_marker)
                        json_data = content[start:end].strip()
                        existing_data = json.loads(json_data)
                    else:
                        # إذا لم توجد العلامات، جرب الطريقة القديمة
                        start = content.find('[')
                        end = content.rfind(']') + 1
                        if start != -1 and end != 0:
                            json_data = content[start:end]
                            existing_data = json.loads(json_data)
                        else:
                            existing_data = []
                else:
                    existing_data = []
        except (FileNotFoundError, json.JSONDecodeError):
            existing_data = []

        # إضافة البيانات الجديدة
        existing_data.append(link_data)

        # كتابة البيانات في تنسيق JS بدون تكرار
        json_data = json.dumps(existing_data, ensure_ascii=False, indent=2)

        js_content = f"""// ملف تسجيل روابط المستخدمين - تم إنشاؤه تلقائياً
// آخر تحديث: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

// بيانات الروابط في تنسيق JSON
/*JSON_START*/
{json_data}
/*JSON_END*/

// إحصائيات سريعة
const stats = {{
    totalLinks: {len(existing_data)},
    lastUpdate: "{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}",
    uniqueUsers: {len(set(item['user_id'] for item in existing_data))},
    uniqueChannels: {len(set(item['channel'] for item in existing_data if item['channel'] != 'غير معروف'))}
}};

// تحميل البيانات من JSON
const userLinks = {json_data};

// وظائف مساعدة
function getUserLinks(userId) {{
    return userLinks.filter(link => link.user_id === userId);
}}

function getChannelLinks(channelName) {{
    return userLinks.filter(link => link.channel === channelName);
}}

function getLinksToday() {{
    const today = new Date().toISOString().split('T')[0];
    return userLinks.filter(link => link.timestamp.startsWith(today));
}}

console.log('تم تحميل', userLinks.length, 'رابط من', stats.uniqueUsers, 'مستخدم');
"""

        # حفظ الملف
        with open(LINKS_LOG_FILE, 'w', encoding='utf-8') as f:
            f.write(js_content)

    except Exception as e:
        # تسجيل الخطأ بصمت دون إظهاره للمستخدم
        logging.error(f"Silent logging error: {e}")

# تكوين العميل
app = Client(
    "my_bot",
    api_id=API_ID,
    api_hash=API_HASH,
    bot_token=BOT_TOKEN,
    workdir=".",  # استخدام المجلد الحالي
    sleep_threshold=30  # زيادة وقت الانتظار بين الطلبات
)


async def check_user_subscription(client, user_id):
    """التحقق من اشتراك المستخدم في القنوات المطلوبة"""
    not_subscribed = []

    for channel in REQUIRED_CHANNELS:
        try:
            # محاولة الحصول على معلومات العضو
            member = await client.get_chat_member(channel["username"], user_id)

            # التحقق من حالة العضوية
            if member.status in ["left", "kicked", "banned"]:
                not_subscribed.append(channel)
            elif member.status == "restricted":
                # إذا كان محظور جزئياً، نعتبره غير مشترك
                not_subscribed.append(channel)

        except Exception as e:
            # إذا فشل التحقق (غالباً يعني أن المستخدم غير مشترك)
            logging.warning(f"Failed to check subscription for {user_id} in {channel['username']}: {e}")
            not_subscribed.append(channel)

    return not_subscribed


async def send_subscription_message(message, not_subscribed_channels):
    """إرسال رسالة طلب الاشتراك"""
    buttons = []
    for channel in not_subscribed_channels:
        buttons.append([InlineKeyboardButton(f"📢 اشترك في {channel['username']}", url=channel["url"])])

    buttons.append([InlineKeyboardButton("✅ تحقق من الاشتراك", callback_data="check_subscription")])

    subscription_text = """
<b>🔒 يجب الاشتراك أولاً للاستخدام</b>

<b>📢 للاستفادة من البوت، يجب الاشتراك في القنوات التالية:</b>

<b>🔹 قناة التحديثات والدعم</b>
<b>🔹 قناة الأدوات المجانية</b>

<b>بعد الاشتراك، اضغط على "تحقق من الاشتراك" ✅</b>
"""

    await message.reply_text(subscription_text, reply_markup=InlineKeyboardMarkup(buttons), parse_mode=enums.ParseMode.HTML)


# تم حذف إعداد أوامر البوت لتبسيط الكود


@app.on_message(filters.command("start"))
async def start_command(client, message):
    user_id = message.from_user.id

    # تسجيل المستخدم في الإحصائيات (مع استثناء المشرفين والحسابات المحددة)
    if STATS_AVAILABLE and user_id not in EXCLUDED_USER_IDS:
        try:
            user_stats.add_new_user(
                user_id=user_id,
                username=message.from_user.username,
                first_name=message.from_user.first_name,
                last_name=message.from_user.last_name
            )
            user_stats.update_user_activity(user_id, "command")
        except Exception as e:
            logger.error(f"خطأ في تسجيل المستخدم في الإحصائيات: {e}")

    # التحقق من الاشتراك في القنوات المطلوبة
    not_subscribed = await check_user_subscription(client, user_id)
    if not_subscribed:
        await send_subscription_message(message, not_subscribed)
        return

    # السماح للجميع بالوصول للقائمة الرئيسية
    # سيتم التحقق من صلاحيات VIP عند استخدام الميزات المتقدمة
    # كود قديم معطل - تم نقل التحقق من VIP للميزات المتقدمة
    if False:
        # إرسال رسالة VIP مباشرة بدلاً من استخدام نظام VIP المعقد
        vip_message = (
            "╔══════ <b>🌟 نظام العضوية المميزة</b> 🌟 ══════╗\n\n"
            "🔒 <b>مرحباً بك في بوت سحب المنشورات المتطور!</b>\n\n"
            "💎 <b>عرض خاص: اشترك في VIP مقابل 1$ شهرياً فقط!</b>\n\n"

            "<b>✨ المزايا الحصرية للأعضاء المميزين:</b>\n"
            "◈ 🚀 سحب المنشورات بسرعة فائقة\n"
            "◈ � روابط مباشرة بدون إعلانات أو اختصارات\n"
            "◈ ⚡ تجاوز صفحات الانتظار والإعلانات\n"
            "◈ 🛡️ دعم فني متميز على مدار الساعة\n"
            "◈ � أولوية في المعالجة والسحب\n"
            "◈ 📊 إحصائيات مفصلة لعمليات السحب\n"
            "◈ 🔥 ميزات حصرية للأعضاء المميزين\n\n"

            "<b>💰 العرض الحصري:</b>\n"
            "◈ 📌 سعر الاشتراك: 1$ شهرياً فقط\n"
            "◈ 🎁 تجربة مجانية للمشتركين الجدد\n"
            "◈ 💸 وفر وقتك وجهدك في سحب المنشورات\n\n"

            "<b>💳 وسائل الدفع المتوفرة:</b>\n"
            "◈ 🟡 <b>Binance Pay</b> – نقبل USDT / BNB / BTC وغيرها\n"
            "◈ 🌐 <b>WebMoney</b> – الدفع عبر WMZ\n"
            "◈ 💸 <b>Payeer</b> – الدفع بالدولار أو الروبل\n"
            "◈ 📲 <b>Telegram Wallet</b> – الدفع عبر المحفظة مباشرة"
        )

        keyboard = InlineKeyboardMarkup([
            [InlineKeyboardButton("💎 اشترك الآن", url=VIP_CONTACT_URL)],
            [InlineKeyboardButton("📢 قناة المحتوى المجاني", url="https://t.me/premuimfreex")]
        ])

        await message.reply_text(
            vip_message,
            reply_markup=keyboard,
            parse_mode=enums.ParseMode.HTML
        )
        return
    # إنشاء لوحة المفاتيح مع توضيح الميزات المجانية و VIP
    if is_vip_system_enabled():
        # النظام المدفوع - عرض الميزات مع تمييز VIP
        keyboard_buttons = [
            [InlineKeyboardButton("🆓 سحب منشور واحد", callback_data="single_post")],
            [InlineKeyboardButton("💎 سحب نطاق محدد", callback_data="range_posts")],
            [InlineKeyboardButton("💎 سحب تسلسلي للخلف", callback_data="backward_posts")],
            [InlineKeyboardButton("💎 سحب تسلسلي للأمام", callback_data="forward_posts")],
            [InlineKeyboardButton("📊 إحصائيات", callback_data="show_stats")]
        ]
    else:
        # النظام المجاني - جميع الميزات متاحة للجميع
        keyboard_buttons = [
            [InlineKeyboardButton("🆓 سحب منشور واحد", callback_data="single_post")],
            [InlineKeyboardButton("🆓 سحب نطاق محدد", callback_data="range_posts")],
            [InlineKeyboardButton("🆓 سحب تسلسلي للخلف", callback_data="backward_posts")],
            [InlineKeyboardButton("🆓 سحب تسلسلي للأمام", callback_data="forward_posts")],
            [InlineKeyboardButton("📊 إحصائيات", callback_data="show_stats")]
        ]

    # إضافة أزرار VIP (فقط في الوضع المدفوع)
    if is_vip_system_enabled():
        if auth_manager.is_authorized_user(user_id):
            keyboard_buttons.append([InlineKeyboardButton("💎 معلومات VIP", callback_data="vip_info")])
        else:
            keyboard_buttons.append([InlineKeyboardButton("💎 اشترك في VIP", callback_data="vip_subscribe")])

    # إضافة لوحة تحكم للمشرفين
    if auth_manager.is_admin(user_id):
        keyboard_buttons.append([InlineKeyboardButton("🎛️ لوحة التحكم", callback_data="admin_control_panel")])

    # إضافة أزرار مختلفة حسب حالة النظام
    if is_vip_system_enabled():
        # في الوضع المدفوع - زر المطور العادي
        keyboard_buttons.append([InlineKeyboardButton("المطور 👨‍💻", url="https://t.me/GurusVIP")])
    else:
        # في الوضع المجاني - زر دعم المطور
        keyboard_buttons.append([InlineKeyboardButton("💝 ادعم المطور (فاتورة الاستضافة)", url="https://linkjust.com/u3TONie")])
        keyboard_buttons.append([InlineKeyboardButton("المطور 👨‍💻", url="https://t.me/GurusVIP")])

    keyboard = InlineKeyboardMarkup(keyboard_buttons)

    # استخدم HTML مع تنسيق قوي لضمان ظهور النص بخط عريض
    if is_vip_system_enabled():
        # النص للنظام المدفوع
        welcome_text = """<b>🌟 مرحباً بك في بوت سحب المنشورات المتطور</b>

<b>🆓 الميزات المجانية:</b>
<b>• سحب منشور واحد - سحب منشور محدد فقط ⚡</b>

<b>💎 ميزات VIP الحصرية:</b>
<b>• سحب نطاق محدد - تحديد أول وآخر منشور للسحب 📊</b>
<b>• سحب تسلسلي للخلف - من رقم معين إلى الأقدم ⬆️</b>
<b>• سحب تسلسلي للأمام - من رقم معين إلى الأحدث ⬇️</b>
<b>• أولوية في المعالجة - سرعة أكبر في السحب 🚀</b>

<b>🎯 ميزات عامة:</b>
<b>• دعم جميع أنواع الوسائط 📱</b>
<b>• واجهة سهلة الاستخدام 🎯</b>

<b>📝 التعليمات:</b>
<b>1️⃣ اختر نوع السحب المطلوب</b>
<b>2️⃣ أرسل رابط المنشور/المنشورات</b>
<b>3️⃣ انتظر اكتمال العملية</b>

<b>⚖️ سياسة الاستخدام:</b>
<b>🔐 البوت يعمل ضمن سياسة استخدام تليجرام</b>
<b>✅ يسحب فقط من القنوات والمجموعات العامة</b>
<b>🚫 لا يملك صلاحيات للوصول إلى أي محتوى خاص</b>
<b>📢 يحترم حقوق النشر والملكية الفكرية</b>

<b>⏱️ ملاحظات مهمة:</b>
<b>🛡️ يوجد تأخير 3 ثوان بين كل منشور للحماية من الحظر</b>
<b>⏸️ فترة راحة 1 دقيقة كل 95 منشور لضمان الأمان التام</b>"""
    else:
        # النص للنظام المجاني
        welcome_text = """<b>🌟 مرحباً بك في بوت سحب المنشورات المتطور</b>

<b>🎉 النسخة المجانية الكاملة!</b>
<b>🆓 جميع الميزات متاحة مجاناً بدون قيود:</b>

<b>✨ الميزات المتاحة:</b>
<b>• سحب منشور واحد - سحب منشور محدد فقط ⚡</b>
<b>• سحب نطاق محدد - تحديد أول وآخر منشور للسحب 📊</b>
<b>• سحب تسلسلي للخلف - من رقم معين إلى الأقدم ⬆️</b>
<b>• سحب تسلسلي للأمام - من رقم معين إلى الأحدث ⬇️</b>

<b>🎯 ميزات عامة:</b>
<b>• دعم جميع أنواع الوسائط 📱</b>
<b>• واجهة سهلة الاستخدام 🎯</b>
<b>• لا توجد حاجة لاشتراك VIP 🚫💎</b>

<b>💝 دعم المطور:</b>
<b>• البوت مجاني بالكامل لكم 🎁</b>
<b>• إذا أردت دعمنا لتسديد فاتورة الاستضافة 💰</b>
<b>• يمكنك التواصل معنا عبر الزر أدناه 👇</b>

<b>📝 التعليمات:</b>
<b>1️⃣ اختر نوع السحب المطلوب</b>
<b>2️⃣ أرسل رابط المنشور/المنشورات</b>
<b>3️⃣ انتظر اكتمال العملية</b>

<b>⚖️ سياسة الاستخدام:</b>
<b>🔐 البوت يعمل ضمن سياسة استخدام تليجرام</b>
<b>✅ يسحب فقط من القنوات والمجموعات العامة</b>
<b>🚫 لا يملك صلاحيات للوصول إلى أي محتوى خاص</b>
<b>📢 يحترم حقوق النشر والملكية الفكرية</b>

<b>⏱️ ملاحظات مهمة:</b>
<b>🛡️ يوجد تأخير 3 ثوان بين كل منشور للحماية من الحظر</b>
<b>⏸️ فترة راحة 1 دقيقة كل 95 منشور لضمان الأمان التام</b>"""

    # اختيار الصورة التالية بدون تكرار
    random_image = get_next_welcome_image()

    try:
        await message.reply_photo(photo=random_image,
                                  caption=welcome_text,
                                  reply_markup=keyboard,
                                  parse_mode=enums.ParseMode.HTML)
    except Exception as e:
        # إذا فشل HTML، جرب Markdown
        logger.warning(f"HTML failed: {e}, trying Markdown")
        markdown_text = """**🌟 مرحباً بك في بوت سحب المنشورات المتطور**

✨ **الميزات المتاحة:**
• **سحب منشور واحد** - **سحب منشور محدد فقط** ⚡
• **سحب نطاق محدد** - **تحديد أول وآخر منشور للسحب** 📊
• **سحب تسلسلي للخلف** - **من رقم معين إلى الأقدم** ⬆️
• **سحب تسلسلي للأمام** - **من رقم معين إلى الأحدث** ⬇️
• **دعم جميع أنواع الوسائط** 📱
• **واجهة سهلة الاستخدام** 🎯

**📝 التعليمات:**
**1️⃣ اختر نوع السحب المطلوب**
**2️⃣ أرسل رابط المنشور/المنشورات**
**3️⃣ انتظر اكتمال العملية**

**⚖️ سياسة الاستخدام:**
**🔐 البوت يعمل ضمن سياسة استخدام تليجرام**
**✅ يسحب فقط من القنوات والمجموعات العامة**
**🚫 لا يملك صلاحيات للوصول إلى أي محتوى خاص**"""

        try:
            await message.reply_photo(photo=random_image,
                                      caption=markdown_text,
                                      reply_markup=keyboard,
                                      parse_mode=enums.ParseMode.MARKDOWN)
        except Exception as e2:
            # إذا فشل كل شيء، أرسل بدون تنسيق
            logger.warning(f"Markdown also failed: {e2}, sending plain text")
            plain_text = """🌟 مرحباً بك في بوت سحب المنشورات المتطور

✨ الميزات المتاحة:
• سحب منشور واحد - سحب منشور محدد فقط ⚡
• سحب نطاق محدد - تحديد أول وآخر منشور للسحب 📊
• سحب تسلسلي للخلف - من رقم معين إلى الأقدم ⬆️
• سحب تسلسلي للأمام - من رقم معين إلى الأحدث ⬇️
• دعم جميع أنواع الوسائط 📱
• واجهة سهلة الاستخدام 🎯

📝 التعليمات:
1️⃣ اختر نوع السحب المطلوب
2️⃣ أرسل رابط المنشور/المنشورات
3️⃣ انتظر اكتمال العملية

⚖️ سياسة الاستخدام:
🔐 البوت يعمل ضمن سياسة استخدام تليجرام
✅ يسحب فقط من القنوات والمجموعات العامة
🚫 لا يملك صلاحيات للوصول إلى أي محتوى خاص"""

            await message.reply_photo(photo=random_image,
                                      caption=plain_text,
                                      reply_markup=keyboard)



# معالج واحد موحد للأزرار
@app.on_callback_query(
    filters.regex(
        "^(single_post|range_posts|forward_posts|backward_posts|stop_download|back_to_menu|show_stats|delete_now|check_subscription|vip_subscribe|vip_info|manage_vip|clean_expired_vip|vip_info_display|remove_vip_.*|admin_panel|delete_vip_.*|vip_page_.*|add_vip_guide|confirm_delete_.*|toggle_vip_system|admin_control_panel|show_detailed_stats|top_users)$"
    ))
async def handle_buttons(client: Client, callback: CallbackQuery):
    try:
        user_id = callback.from_user.id
        data = callback.data

        # إضافة معالجة زر delete_now
        if data == "delete_now":
            await callback.message.delete()
            return

        # معالجة زر التحقق من الاشتراك
        if data == "check_subscription":
            not_subscribed = await check_user_subscription(client, user_id)
            if not_subscribed:
                await callback.answer("❌ يجب الاشتراك في جميع القنوات أولاً!", show_alert=True)
                return
            else:
                await callback.answer("✅ تم التحقق بنجاح! يمكنك الآن استخدام البوت", show_alert=True)
                # إعادة توجيه للقائمة الرئيسية
                await start_command(client, callback.message)
                return

        # التحقق من الاشتراك قبل أي عملية
        not_subscribed = await check_user_subscription(client, user_id)
        if not_subscribed:
            await callback.answer("❌ يجب الاشتراك في جميع القنوات للاستخدام!", show_alert=True)
            await send_subscription_message(callback.message, not_subscribed)
            return

        if data == "single_post":
            # تسجيل النشاط في الإحصائيات (مع استثناء المشرفين والحسابات المحددة)
            if STATS_AVAILABLE and user_id not in EXCLUDED_USER_IDS:
                try:
                    user_stats.update_user_activity(user_id, "single_posts")
                except Exception as e:
                    logger.error(f"خطأ في تسجيل نشاط single_post: {e}")

            await safe_edit_message(
                callback.message,
                "<b>قم بإرسال رابط المنشور الذي تريد سحبه</b>\n"
                "<b>مثال: https://t.me/channel_name/123</b>",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("🔙 رجوع",
                                         callback_data="back_to_menu")
                ]]))
            # تعيين حالة المستخدم لسحب منشور واحد
            user_states[user_id] = "waiting_for_single_post"

        elif data == "range_posts":
            # التحقق من صلاحيات VIP للميزات المتقدمة (إذا كان النظام مفعل)
            if is_vip_system_enabled() and not auth_manager.is_authorized_user(user_id):
                await show_vip_required_message(callback)
                return

            # تسجيل النشاط في الإحصائيات (مع استثناء المشرفين والحسابات المحددة)
            if STATS_AVAILABLE and user_id not in EXCLUDED_USER_IDS:
                try:
                    user_stats.update_user_activity(user_id, "range_posts")
                    if auth_manager.is_authorized_user(user_id):
                        user_stats.update_user_activity(user_id, "vip_actions")
                except Exception as e:
                    logger.error(f"خطأ في تسجيل نشاط range_posts: {e}")

            await safe_edit_message(
                callback.message,
                "<b>سحب نطاق محدد من المنشورات</b>\n\n"
                "<b>قم بإرسال رابط أول منشور تريد البدء منه:</b>\n"
                "<b>مثال: https://t.me/channel_name/100</b>\n\n"
                "<b>بعدها سيُطلب منك رابط آخر منشور</b>",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("🔙 رجوع",
                                         callback_data="back_to_menu")
                ]]))
            user_states[user_id] = "waiting_for_range_start"

        elif data == "forward_posts":
            # التحقق من صلاحيات VIP للميزات المتقدمة (إذا كان النظام مفعل)
            if is_vip_system_enabled() and not auth_manager.is_authorized_user(user_id):
                await show_vip_required_message(callback)
                return

            # تسجيل النشاط في الإحصائيات (مع استثناء المشرفين والحسابات المحددة)
            if STATS_AVAILABLE and user_id not in EXCLUDED_USER_IDS:
                try:
                    user_stats.update_user_activity(user_id, "forward_posts")
                    if auth_manager.is_authorized_user(user_id):
                        user_stats.update_user_activity(user_id, "vip_actions")
                except Exception as e:
                    logger.error(f"خطأ في تسجيل نشاط forward_posts: {e}")

            await safe_edit_message(
                callback.message,
                "<b>سحب تسلسلي للأمام</b>\n\n"
                "<b>قم بإرسال رابط المنشور الذي تريد البدء منه:</b>\n"
                "<b>مثال: https://t.me/channel_name/3605</b>\n\n"
                "<b>سيتم سحب المنشورات تسلسلياً من هذا الرقم للأمام</b>",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("🔙 رجوع",
                                         callback_data="back_to_menu")
                ]]))
            user_states[user_id] = "waiting_for_forward_posts"

        elif data == "backward_posts":
            # التحقق من صلاحيات VIP للميزات المتقدمة (إذا كان النظام مفعل)
            if is_vip_system_enabled() and not auth_manager.is_authorized_user(user_id):
                await show_vip_required_message(callback)
                return

            # تسجيل النشاط في الإحصائيات (مع استثناء المشرفين والحسابات المحددة)
            if STATS_AVAILABLE and user_id not in EXCLUDED_USER_IDS:
                try:
                    user_stats.update_user_activity(user_id, "backward_posts")
                    if auth_manager.is_authorized_user(user_id):
                        user_stats.update_user_activity(user_id, "vip_actions")
                except Exception as e:
                    logger.error(f"خطأ في تسجيل نشاط backward_posts: {e}")

            await safe_edit_message(
                callback.message,
                "<b>سحب تسلسلي للخلف</b>\n\n"
                "<b>قم بإرسال رابط المنشور الذي تريد البدء منه:</b>\n"
                "<b>مثال: https://t.me/channel_name/3605</b>\n\n"
                "<b>سيتم سحب المنشورات تسلسلياً من هذا الرقم للخلف</b>",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("🔙 رجوع",
                                         callback_data="back_to_menu")
                ]]))
            user_states[user_id] = "waiting_for_backward_posts"

        elif data == "stop_download":
            if user_id in active_downloads:
                active_downloads[user_id] = False
                await callback.answer("تم إيقاف عملية السحب! ✅")
            else:
                await callback.answer("لا توجد عملية سحب نشطة! ❌")

        elif data == "back_to_menu":
            # إزالة حالة المستخدم عند العودة للقائمة الرئيسية
            user_states.pop(user_id, None)

            # إنشاء لوحة المفاتيح مع توضيح الميزات المجانية و VIP
            if is_vip_system_enabled():
                # النظام المدفوع - عرض الميزات مع تمييز VIP
                keyboard_buttons = [
                    [InlineKeyboardButton("🆓 سحب منشور واحد", callback_data="single_post")],
                    [InlineKeyboardButton("💎 سحب نطاق محدد", callback_data="range_posts")],
                    [InlineKeyboardButton("💎 سحب تسلسلي للخلف", callback_data="backward_posts")],
                    [InlineKeyboardButton("💎 سحب تسلسلي للأمام", callback_data="forward_posts")],
                    [InlineKeyboardButton("📊 إحصائيات", callback_data="show_stats")]
                ]
            else:
                # النظام المجاني - جميع الميزات متاحة للجميع
                keyboard_buttons = [
                    [InlineKeyboardButton("🆓 سحب منشور واحد", callback_data="single_post")],
                    [InlineKeyboardButton("🆓 سحب نطاق محدد", callback_data="range_posts")],
                    [InlineKeyboardButton("🆓 سحب تسلسلي للخلف", callback_data="backward_posts")],
                    [InlineKeyboardButton("🆓 سحب تسلسلي للأمام", callback_data="forward_posts")],
                    [InlineKeyboardButton("📊 إحصائيات", callback_data="show_stats")]
                ]

            # إضافة أزرار VIP (فقط في الوضع المدفوع)
            if is_vip_system_enabled():
                if auth_manager.is_authorized_user(user_id):
                    keyboard_buttons.append([InlineKeyboardButton("💎 معلومات VIP", callback_data="vip_info")])
                else:
                    keyboard_buttons.append([InlineKeyboardButton("💎 اشترك في VIP", callback_data="vip_subscribe")])

            # إضافة لوحة تحكم للمشرفين
            if auth_manager.is_admin(user_id):
                keyboard_buttons.append([InlineKeyboardButton("🎛️ لوحة التحكم", callback_data="admin_control_panel")])

            # إضافة أزرار مختلفة حسب حالة النظام
            if is_vip_system_enabled():
                # في الوضع المدفوع - زر المطور العادي
                keyboard_buttons.append([InlineKeyboardButton("المطور 👨‍💻", url="https://t.me/GurusVIP")])
            else:
                # في الوضع المجاني - زر دعم المطور
                keyboard_buttons.append([InlineKeyboardButton("💝 ادعم المطور (فاتورة الاستضافة)", url="https://linkjust.com/u3TONie")])
                keyboard_buttons.append([InlineKeyboardButton("المطور 👨‍💻", url="https://t.me/GurusVIP")])

            keyboard = InlineKeyboardMarkup(keyboard_buttons)

            if is_vip_system_enabled():
                # النص للنظام المدفوع
                welcome_text = """
<b>🌟 مرحباً بك في بوت سحب المنشورات المتطور</b>

<b>🆓 الميزات المجانية:</b>
<b>• سحب منشور واحد - سحب منشور محدد فقط ⚡</b>

<b>💎 ميزات VIP الحصرية:</b>
<b>• سحب نطاق محدد - تحديد أول وآخر منشور للسحب 📊</b>
<b>• سحب تسلسلي للخلف - من رقم معين إلى الأقدم ⬆️</b>
<b>• سحب تسلسلي للأمام - من رقم معين إلى الأحدث ⬇️</b>

<b>⚖️ سياسة الاستخدام:</b>
<b>🔐 البوت يعمل ضمن سياسة استخدام تليجرام</b>
<b>✅ يسحب فقط من القنوات والمجموعات العامة</b>
<b>🚫 لا يملك صلاحيات للوصول إلى أي محتوى خاص</b>

<b>اختر الخيار المناسب لك:</b>
"""
            else:
                # النص للنظام المجاني
                welcome_text = """
<b>🌟 مرحباً بك في بوت سحب المنشورات المتطور</b>

<b>🎉 النسخة المجانية الكاملة!</b>
<b>🆓 جميع الميزات متاحة مجاناً بدون قيود:</b>

<b>✨ الميزات المتاحة:</b>
<b>• سحب منشور واحد - سحب منشور محدد فقط ⚡</b>
<b>• سحب نطاق محدد - تحديد أول وآخر منشور للسحب 📊</b>
<b>• سحب تسلسلي للخلف - من رقم معين إلى الأقدم ⬆️</b>
<b>• سحب تسلسلي للأمام - من رقم معين إلى الأحدث ⬇️</b>

<b>🎯 ميزات إضافية:</b>
<b>• لا توجد حاجة لاشتراك VIP 🚫💎</b>
<b>• جميع الميزات مفتوحة للجميع 🔓</b>

<b>💝 دعم المطور:</b>
<b>• البوت مجاني بالكامل لكم 🎁</b>
<b>• إذا أردت دعمنا لتسديد فاتورة الاستضافة 💰</b>

<b>⚖️ سياسة الاستخدام:</b>
<b>🔐 البوت يعمل ضمن سياسة استخدام تليجرام</b>
<b>✅ يسحب فقط من القنوات والمجموعات العامة</b>
<b>🚫 لا يملك صلاحيات للوصول إلى أي محتوى خاص</b>

<b>اختر الخيار المناسب لك:</b>
"""
            try:
                await callback.message.edit_text(welcome_text, reply_markup=keyboard, parse_mode=enums.ParseMode.HTML)
            except Exception as e:
                if "MESSAGE_NOT_MODIFIED" in str(e):
                    # الرسالة لم تتغير، لا نحتاج لفعل شيء
                    pass
                else:
                    # خطأ آخر، نسجله
                    logging.error(f"Error editing back to menu message: {e}")

        elif data == "show_stats":
            if STATS_AVAILABLE:
                try:
                    # الحصول على الإحصائيات
                    total_users = user_stats.get_total_users()
                    active_today = user_stats.get_active_users_today()
                    new_today = user_stats.get_new_users_today()
                    uptime = user_stats.get_bot_uptime()

                    # إحصائيات اليوم
                    daily_stats = user_stats.get_daily_stats()

                    # إحصائيات الأسبوع
                    weekly_stats = user_stats.get_weekly_stats()

                    stats_text = f"""
<b>📊 إحصائيات البوت المتقدمة</b>

<b>👥 إحصائيات المستخدمين:</b>
<b>• إجمالي المستخدمين: {total_users:,}</b>
<b>• نشطين اليوم: {active_today:,}</b>
<b>• جدد اليوم: {new_today:,}</b>
<b>• نشطين هذا الأسبوع: {weekly_stats.get('active_users', 0):,}</b>

<b>📈 إحصائيات اليوم:</b>
<b>• إجمالي الأوامر: {daily_stats.get('total_commands', 0):,}</b>
<b>• سحب منشور واحد: {daily_stats.get('single_posts', 0):,}</b>
<b>• سحب نطاق محدد: {daily_stats.get('range_posts', 0):,}</b>
<b>• سحب تسلسلي للأمام: {daily_stats.get('forward_posts', 0):,}</b>
<b>• سحب تسلسلي للخلف: {daily_stats.get('backward_posts', 0):,}</b>
<b>• عمليات VIP: {daily_stats.get('vip_actions', 0):,}</b>

<b>📅 إحصائيات الأسبوع:</b>
<b>• مستخدمين جدد: {weekly_stats.get('new_users', 0):,}</b>
<b>• إجمالي الأوامر: {weekly_stats.get('total_commands', 0):,}</b>

<b>⏱️ معلومات النظام:</b>
<b>• مدة التشغيل: {uptime}</b>
<b>• حالة البوت: 🟢 يعمل بشكل مثالي</b>

<b>🚀 تم تطوير البوت بواسطة: @GurusVIP</b>
"""

                    keyboard = InlineKeyboardMarkup([
                        [InlineKeyboardButton("🔄 تحديث الإحصائيات", callback_data="show_stats")],
                        [InlineKeyboardButton("👑 أكثر المستخدمين نشاطاً", callback_data="top_users")],
                        [InlineKeyboardButton("🔙 رجوع", callback_data="back_to_menu")]
                    ])

                except Exception as e:
                    logger.error(f"خطأ في جلب الإحصائيات: {e}")
                    stats_text = f"""
<b>📊 إحصائيات البوت:</b>

<b>⚠️ خطأ في تحميل الإحصائيات المفصلة</b>
<b>🤖 البوت يعمل بشكل طبيعي</b>
<b>✅ جميع الميزات متاحة</b>

<b>🚀 تم تطوير البوت بواسطة: @GurusVIP</b>
"""
                    keyboard = InlineKeyboardMarkup([
                        [InlineKeyboardButton("🔙 رجوع", callback_data="back_to_menu")]
                    ])
            else:
                stats_text = """
<b>📊 إحصائيات البوت:</b>

<b>🤖 البوت يعمل بشكل طبيعي</b>
<b>✅ جميع الميزات متاحة</b>
<b>🔄 جاهز لسحب المنشورات</b>

<b>⚠️ نظام الإحصائيات المتقدم غير متاح</b>

<b>🚀 تم تطوير البوت بواسطة: @GurusVIP</b>
"""
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton("🔙 رجوع", callback_data="back_to_menu")]
                ])

            try:
                await callback.message.edit_text(stats_text, reply_markup=keyboard, parse_mode=enums.ParseMode.HTML)
            except Exception as e:
                if "MESSAGE_NOT_MODIFIED" in str(e):
                    pass
                else:
                    logging.error(f"Error editing stats message: {e}")

        elif data == "top_users":
            if STATS_AVAILABLE:
                try:
                    top_users = user_stats.get_top_users(10)

                    if top_users:
                        top_users_text = "<b>👑 أكثر المستخدمين نشاطاً</b>\n\n"

                        for i, user in enumerate(top_users, 1):
                            name = user.get('name', 'غير معروف')
                            username = user.get('username')
                            commands = user.get('total_commands', 0)

                            # إضافة رمز التاج للأوائل
                            if i == 1:
                                emoji = "🥇"
                            elif i == 2:
                                emoji = "🥈"
                            elif i == 3:
                                emoji = "🥉"
                            else:
                                emoji = f"{i}️⃣"

                            user_info = f"{emoji} <b>{name}</b>"
                            if username:
                                user_info += f" (@{username})"
                            user_info += f"\n   📊 <b>{commands:,}</b> أمر\n\n"

                            top_users_text += user_info

                        top_users_text += "<b>🎉 شكراً لجميع المستخدمين النشطين!</b>"
                    else:
                        top_users_text = """
<b>👑 أكثر المستخدمين نشاطاً</b>

<b>📊 لا توجد بيانات كافية بعد</b>
<b>🚀 ابدأ باستخدام البوت لتظهر في القائمة!</b>
"""

                    keyboard = InlineKeyboardMarkup([
                        [InlineKeyboardButton("📊 العودة للإحصائيات", callback_data="show_stats")],
                        [InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="back_to_menu")]
                    ])

                    await callback.message.edit_text(top_users_text, reply_markup=keyboard, parse_mode=enums.ParseMode.HTML)

                except Exception as e:
                    logger.error(f"خطأ في عرض أكثر المستخدمين نشاطاً: {e}")
                    await callback.answer("❌ خطأ في تحميل البيانات", show_alert=True)
            else:
                await callback.answer("⚠️ نظام الإحصائيات غير متاح", show_alert=True)

        # معالجة أزرار VIP
        elif data == "vip_subscribe":
            # عرض معلومات الاشتراك في VIP
            vip_message = (
                "💎 <b>اشترك في خدمة VIP الآن!</b> 💎\n\n"
                "🚀 <b>استفد من أقوى بوت سحب المنشورات!</b>\n\n"
                "✅ سحب المنشورات بسرعة فائقة\n"
                "✅ دعم فني مميز وسريع\n"
                "✅ أولوية في المعالجة والسحب\n"
                "✅ وفر وقتك وجهدك في سحب المحتوى!\n\n"
                "💸 <b>العرض الحالي:</b>\n"
                "📆 اشتراك لمدة شهر واحد فقط بـ 1 دولار!\n"
                "⚠️ العرض محدود — سارع بالاشتراك الآن!\n\n"
                "📲 للاشتراك، تواصل مع المسؤول:\n"
                f"👤 @GurusVIP"
            )

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton("💬 تواصل مع المسؤول", url=VIP_CONTACT_URL)]
            ])

            try:
                await callback.message.edit_text(
                    vip_message,
                    reply_markup=keyboard,
                    parse_mode=enums.ParseMode.HTML
                )
            except Exception as e:
                if "MESSAGE_NOT_MODIFIED" in str(e):
                    # الرسالة لم تتغير، لا نحتاج لفعل شيء
                    pass
                else:
                    # خطأ آخر، نسجله
                    logging.error(f"Error editing VIP message: {e}")

        elif data == "vip_info":
            # عرض معلومات VIP للمستخدم
            if auth_manager.is_authorized_user(user_id):
                vip_info = vip_manager.get_vip_info(user_id)
                user_name = callback.from_user.first_name

                if vip_info:
                    from datetime import datetime
                    joined_date = vip_info.get("joined_at", "غير محدد")
                    expiry_date = vip_info.get("expires_at", "غير محدد")
                    days_total = vip_info.get("days_total", 0)

                    # حساب الأيام المتبقية
                    try:
                        expiry = datetime.strptime(expiry_date, "%Y-%m-%d").date()
                        days_left = (expiry - datetime.now().date()).days
                        days_left_text = f"{days_left} يوم" if days_left > 0 else "منتهي"
                    except:
                        days_left_text = "غير محدد"

                    message = (
                        "💎 <b>معلومات اشتراك VIP</b> 💎\n\n"
                        f"👤 المستخدم: {user_name}\n"
                        f"📅 تاريخ الاشتراك: {joined_date}\n"
                        f"⏳ تاريخ الانتهاء: {expiry_date}\n"
                        f"⌛ المدة المتبقية: {days_left_text}\n"
                        f"📊 مدة الاشتراك: {days_total} يوم\n\n"
                        "✅ أنت تتمتع بجميع مميزات VIP!\n"
                        "• دعم فني مميز"
                    )
                else:
                    # في حالة كان المستخدم مشرفًا (VIP تلقائي)
                    message = (
                        "💎 <b>معلومات اشتراك VIP</b> 💎\n\n"
                        f"👤 المستخدم: {user_name}\n"
                        "🔑 الحالة: مشرف (VIP تلقائي)\n\n"
                        "✅ أنت تتمتع بجميع مميزات VIP!\n"
                        "• دعم فني مميز"
                    )
            else:
                # رسالة للمستخدمين غير VIP
                message = (
                    "💎 <b>معلومات اشتراك VIP</b> 💎\n\n"
                    "❌ أنت غير مشترك في خدمة VIP.\n\n"
                    "🚀 <b>استفد من أقوى بوت سحب المنشورات الآن!</b>\n\n"
                    "✅ سحب المنشورات بسرعة فائقة\n"
                    "✅ دعم فني مميز وسريع\n"
                    "✅ أولوية في المعالجة والسحب\n"
                    "✅ وفر وقتك وجهدك في سحب المحتوى!\n\n"
                    "💸 <b>العرض الحالي:</b>\n"
                    "📆 اشتراك لمدة شهر واحد فقط بـ 1 دولار!\n"
                    "🎯 استثمر في راحتك بأقل من ثمن كوب شاي يوميًا\n"
                    "⚠️ العرض محدود — سارع بالاشتراك الآن!\n\n"
                    "📩 للاشتراك والتواصل:\n"
                    f"👤 @GurusVIP"
                )

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton("💬 تواصل مع المسؤول", url=VIP_CONTACT_URL)]
            ])

            try:
                await callback.message.edit_text(
                    message,
                    reply_markup=keyboard,
                    parse_mode=enums.ParseMode.HTML
                )
            except Exception as e:
                if "MESSAGE_NOT_MODIFIED" in str(e):
                    # الرسالة لم تتغير، لا نحتاج لفعل شيء
                    pass
                else:
                    # خطأ آخر، نسجله
                    logging.error(f"Error editing VIP info message: {e}")

        elif data == "manage_vip":
            # التحقق من صلاحيات المشرف
            if not auth_manager.is_admin(user_id):
                await callback.answer("⚠️ هذه الميزة متاحة للمشرفين فقط", show_alert=True)
                return

            await show_vip_management_table(callback, page=0)

        elif data == "clean_expired_vip":
            # التحقق من صلاحيات المشرف
            if not auth_manager.is_admin(user_id):
                await callback.answer("⚠️ هذه الميزة متاحة للمشرفين فقط", show_alert=True)
                return

            # تنظيف العضويات المنتهية
            removed_count, removed_names = vip_manager.clean_expired_vip()

            if removed_count > 0:
                result_message = f"✅ تم حذف {removed_count} عضو VIP منتهي الصلاحية:\n\n"
                result_message += "\n".join(removed_names[:5])  # عرض أول 5 أعضاء فقط
                if len(removed_names) > 5:
                    result_message += f"\n... و {len(removed_names) - 5} عضو آخر"
            else:
                result_message = "✅ لا يوجد أعضاء VIP منتهية الصلاحية للحذف"

            await callback.answer(result_message, show_alert=True)

        elif data.startswith("remove_vip_"):
            # التحقق من صلاحيات المشرف
            if not auth_manager.is_admin(user_id):
                await callback.answer("⚠️ هذه الميزة متاحة للمشرفين فقط", show_alert=True)
                return

            # استخراج معرف المستخدم
            user_id_to_remove = data.replace('remove_vip_', '')
            try:
                user_id_to_remove = int(user_id_to_remove)

                # الحصول على معلومات المستخدم قبل الحذف
                vip_info = vip_manager.get_vip_info(user_id_to_remove)
                user_name = vip_info.get("name", "غير معروف") if vip_info else "غير معروف"

                # حذف المستخدم
                if vip_manager.remove_vip(user_id_to_remove):
                    await callback.answer(f"✅ تم حذف العضو: {user_name}", show_alert=True)
                else:
                    await callback.answer("❌ فشل في حذف العضو", show_alert=True)
            except:
                await callback.answer("❌ معرف المستخدم غير صالح", show_alert=True)

        elif data.startswith("delete_vip_"):
            # التحقق من صلاحيات المشرف
            if not auth_manager.is_admin(user_id):
                await callback.answer("⚠️ هذه الميزة متاحة للمشرفين فقط", show_alert=True)
                return

            # استخراج معرف المستخدم
            user_id_to_delete = data.replace('delete_vip_', '')
            try:
                user_id_to_delete = int(user_id_to_delete)

                # الحصول على معلومات المستخدم قبل الحذف
                vip_info = vip_manager.get_vip_info(user_id_to_delete)
                if not vip_info:
                    await callback.answer("❌ العضو غير موجود", show_alert=True)
                    return

                user_name = vip_info.get("name", "غير معروف")

                # عرض رسالة تأكيد مفصلة
                expiry_date = vip_info.get('expires_at', 'غير محدد')
                joined_date = vip_info.get('joined_at', 'غير محدد')

                # تحديد حالة العضوية
                try:
                    from datetime import datetime
                    expiry = datetime.strptime(expiry_date, "%Y-%m-%d").date()
                    current_date = datetime.now().date()

                    if expiry >= current_date:
                        days_left = (expiry - current_date).days
                        if days_left > 7:
                            status = "🟢 نشط"
                            status_detail = f"({days_left} يوم متبقي)"
                        else:
                            status = "🟡 ينتهي قريباً"
                            status_detail = f"({days_left} يوم متبقي)"
                    else:
                        status = "🔴 منتهي"
                        days_expired = (current_date - expiry).days
                        status_detail = f"(منتهي منذ {days_expired} يوم)"
                except:
                    status = "⚠️ خطأ"
                    status_detail = ""

                confirm_message = (
                    f"🗑️ <b>تأكيد حذف عضو VIP</b>\n\n"
                    f"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n"
                    f"👤 <b>الاسم:</b> {user_name}\n"
                    f"🆔 <b>المعرف:</b> <code>{user_id_to_delete}</code>\n"
                    f"📅 <b>انضم في:</b> {joined_date}\n"
                    f"⏰ <b>ينتهي في:</b> {expiry_date}\n"
                    f"📊 <b>الحالة:</b> {status} {status_detail}\n"
                    f"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n"
                    f"⚠️ <b>تحذير:</b> هذا الإجراء لا يمكن التراجع عنه!\n\n"
                    f"❓ هل أنت متأكد من حذف هذا العضو نهائياً؟"
                )

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton("🗑️ نعم، احذف نهائياً", callback_data=f"confirm_delete_{user_id_to_delete}")],
                    [InlineKeyboardButton("❌ إلغاء العملية", callback_data="manage_vip")]
                ])

                await safe_edit_message(callback.message, confirm_message, reply_markup=keyboard)

            except ValueError:
                await callback.answer("❌ معرف المستخدم غير صالح", show_alert=True)

        elif data.startswith("confirm_delete_"):
            # التحقق من صلاحيات المشرف
            if not auth_manager.is_admin(user_id):
                await callback.answer("⚠️ هذه الميزة متاحة للمشرفين فقط", show_alert=True)
                return

            # استخراج معرف المستخدم
            user_id_to_delete = data.replace('confirm_delete_', '')
            try:
                user_id_to_delete = int(user_id_to_delete)

                # الحصول على معلومات المستخدم قبل الحذف
                vip_info = vip_manager.get_vip_info(user_id_to_delete)
                user_name = vip_info.get("name", "غير معروف") if vip_info else "غير معروف"

                # حذف المستخدم
                if vip_manager.remove_vip(user_id_to_delete):
                    await callback.answer(f"✅ تم حذف العضو: {user_name}", show_alert=True)
                    # العودة لجدول الإدارة
                    await show_vip_management_table(callback, page=0)
                else:
                    await callback.answer("❌ فشل في حذف العضو", show_alert=True)
                    await show_vip_management_table(callback, page=0)
            except ValueError:
                await callback.answer("❌ معرف المستخدم غير صالح", show_alert=True)

        elif data.startswith("vip_page_"):
            # التحقق من صلاحيات المشرف
            if not auth_manager.is_admin(user_id):
                await callback.answer("⚠️ هذه الميزة متاحة للمشرفين فقط", show_alert=True)
                return

            # استخراج رقم الصفحة
            try:
                page_num = int(data.replace('vip_page_', ''))
                await show_vip_management_table(callback, page=page_num)
            except ValueError:
                await callback.answer("❌ رقم صفحة غير صالح", show_alert=True)

        elif data == "add_vip_guide":
            # التحقق من صلاحيات المشرف
            if not auth_manager.is_admin(user_id):
                await callback.answer("⚠️ هذه الميزة متاحة للمشرفين فقط", show_alert=True)
                return

            guide_message = (
                "➕ <b>دليل إضافة عضو VIP</b>\n\n"
                "<b>📝 استخدم الأمر التالي:</b>\n"
                "<code>/add_vip [معرف_المستخدم] [عدد_الأيام] [اسم_المستخدم]</code>\n\n"
                "<b>🔸 أمثلة:</b>\n"
                "• <code>/add_vip 123456789 30 أحمد محمد</code>\n"
                "• <code>/add_vip 987654321 7 سارة أحمد</code>\n"
                "• <code>/add_vip 555666777 365 عضو مميز</code>\n\n"
                "<b>📋 شرح المعاملات:</b>\n"
                "• <b>معرف المستخدم:</b> رقم ID الخاص بالمستخدم\n"
                "• <b>عدد الأيام:</b> مدة الاشتراك بالأيام\n"
                "• <b>اسم المستخدم:</b> اسم للتعرف على العضو\n\n"
                "<b>💡 نصائح:</b>\n"
                "• يمكن الحصول على معرف المستخدم من @userinfobot\n"
                "• الحد الأدنى: يوم واحد\n"
                "• الحد الأقصى: 3650 يوم (10 سنوات)"
            )

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton("🔙 العودة للجدول", callback_data="manage_vip")]
            ])

            await safe_edit_message(callback.message, guide_message, reply_markup=keyboard)

        elif data == "vip_info_display":
            await callback.answer("ℹ️ معلومات العضو", show_alert=False)

        elif data == "admin_control_panel":
            # عرض لوحة التحكم للمشرفين
            await show_admin_control_panel(callback)

        elif data == "toggle_vip_system":
            # التحقق من صلاحيات المشرف
            if not auth_manager.is_admin(user_id):
                await callback.answer("⚠️ هذه الميزة متاحة للمشرفين فقط", show_alert=True)
                return

            # تبديل حالة نظام VIP
            new_status = toggle_vip_system()
            mode_text = "المدفوع" if new_status else "المجاني"

            await callback.answer(f"✅ تم تبديل نظام VIP إلى الوضع {mode_text}", show_alert=True)

            # تحديث لوحة التحكم
            await show_admin_control_panel(callback)

        elif data == "show_detailed_stats":
            # التحقق من صلاحيات المشرف
            if not auth_manager.is_admin(user_id):
                await callback.answer("⚠️ هذه الميزة متاحة للمشرفين فقط", show_alert=True)
                return

            # عرض الإحصائيات التفصيلية
            stats = vip_manager.get_vip_statistics()
            vip_status = "🟢 مفعل" if is_vip_system_enabled() else "🔴 معطل"
            mode_text = "المدفوع" if is_vip_system_enabled() else "المجاني"

            detailed_stats_text = f"""
<b>📊 الإحصائيات التفصيلية</b>

<b>🎛️ حالة النظام:</b>
<b>• نظام VIP: {vip_status}</b>
<b>• الوضع الحالي: {mode_text}</b>

<b>👥 إحصائيات الأعضاء:</b>
<b>• إجمالي أعضاء VIP: {stats['total']}</b>
<b>• الأعضاء النشطين: {stats['active']} ✅</b>
<b>• العضويات المنتهية: {stats['expired']} ❌</b>
<b>• تنتهي خلال أسبوع: {stats['expiring_soon']} ⚠️</b>

<b>📈 معدلات الأداء:</b>
"""

            if stats['total'] > 0:
                active_rate = (stats['active'] * 100) // stats['total']
                expired_rate = (stats['expired'] * 100) // stats['total']
                detailed_stats_text += f"""<b>• معدل النشاط: {active_rate}%</b>
<b>• معدل الانتهاء: {expired_rate}%</b>"""
            else:
                detailed_stats_text += "<b>• لا توجد بيانات كافية</b>"

            detailed_stats_text += f"""

<b>🔧 إجراءات مقترحة:</b>
"""

            if stats['expiring_soon'] > 0:
                detailed_stats_text += f"<b>• تذكير {stats['expiring_soon']} عضو بتجديد الاشتراك</b>\n"

            if stats['expired'] > 0:
                detailed_stats_text += f"<b>• تنظيف {stats['expired']} عضوية منتهية</b>\n"

            if stats['active'] == 0 and stats['total'] == 0:
                detailed_stats_text += "<b>• إضافة أعضاء VIP جدد</b>\n"

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton("🔄 تحديث الإحصائيات", callback_data="show_detailed_stats")],
                [InlineKeyboardButton("🔙 العودة للوحة التحكم", callback_data="admin_control_panel")]
            ])

            await safe_edit_message(callback.message, detailed_stats_text, reply_markup=keyboard)

        elif data == "admin_panel":
            # العودة لقائمة الإدارة الرئيسية
            await callback.answer("🔙 العودة للوحة الإدارة")

        await callback.answer()

    except Exception as e:
        logging.error(f"Error in callback handler: {e}")
        await callback.answer("حدث خطأ! ❌")


# تم حذف تسجيل معالج الأزرار لأن @app.on_callback_query يقوم بذلك تلقائياً


# إضافة معالجات للأوامر
@app.on_message(filters.command("help"))
async def help_command(_, message):
    help_text = """
<b>🔍 دليل استخدام البوت:</b>

<b>🆓 الميزات المجانية:</b>
<b>1️⃣ سحب منشور واحد:</b>
<b>- اختر "🆓 سحب منشور واحد"</b>
<b>- أرسل رابط المنشور المراد سحبه</b>

<b>💎 ميزات VIP الحصرية:</b>
<b>2️⃣ سحب نطاق محدد:</b>
<b>- اختر "💎 سحب نطاق محدد"</b>
<b>- أرسل رابط أول منشور</b>
<b>- أرسل رابط آخر منشور</b>
<b>- سيتم سحب جميع المنشورات في النطاق</b>

<b>3️⃣ سحب تسلسلي للخلف:</b>
<b>- اختر "💎 سحب تسلسلي للخلف"</b>
<b>- أرسل رابط منشور البداية</b>
<b>- سيتم السحب من هذا الرقم للخلف</b>

<b>4️⃣ سحب تسلسلي للأمام:</b>
<b>- اختر "💎 سحب تسلسلي للأمام"</b>
<b>- أرسل رابط منشور البداية</b>
<b>- سيتم السحب من هذا الرقم للأمام</b>

<b>5️⃣ الأوامر المتاحة:</b>
<b>/start - بدء استخدام البوت</b>
<b>/help - عرض هذه المساعدة</b>
<b>/cancel - إلغاء العملية الحالية</b>
<b>/settings - إعدادات البوت</b>
<b>/stats - عرض إحصائيات السحب</b>

<b>6️⃣ أوامر VIP:</b>
<b>/vip_info - عرض معلومات اشتراك VIP</b>
<b>/add_vip - إضافة عضو VIP (للمشرفين)</b>
<b>/remove_vip - حذف عضو VIP (للمشرفين)</b>
<b>/extend_vip - تمديد اشتراك VIP (للمشرفين)</b>
<b>/manage_vip - إدارة أعضاء VIP (للمشرفين)</b>
<b>/clean_vip - تنظيف العضويات المنتهية (للمشرفين)</b>
<b>/toggle_vip - تبديل النظام بين المجاني والمدفوع (للمشرفين)</b>

<b>⚖️ سياسة الاستخدام:</b>
<b>🔐 البوت يعمل ضمن سياسة استخدام تليجرام</b>
<b>✅ يسحب فقط من القنوات والمجموعات العامة</b>
<b>🚫 لا يملك صلاحيات للوصول إلى أي محتوى خاص</b>
<b>📢 يحترم حقوق النشر والملكية الفكرية</b>

<b>⏱️ ملاحظات مهمة:</b>
<b>🛡️ يوجد تأخير 3 ثوان بين كل منشور للحماية من الحظر</b>
<b>⏸️ فترة راحة 1 دقيقة كل 95 منشور لضمان الأمان التام</b>

"""
    await message.reply_text(help_text, parse_mode=enums.ParseMode.HTML)


@app.on_message(filters.command("settings"))
async def settings_command(_, message):
    settings_text = """
<b>⚙️ إعدادات البوت:</b>

<b>🔹 لا توجد إعدادات متاحة حالياً</b>
<b>🔸 سيتم إضافة المزيد من الإعدادات قريباً</b>

<b>⚖️ سياسة الاستخدام:</b>
<b>🔐 البوت يعمل ضمن سياسة استخدام تليجرام</b>
<b>✅ يسحب فقط من القنوات والمجموعات العامة</b>
<b>🚫 لا يملك صلاحيات للوصول إلى أي محتوى خاص</b>


"""
    await message.reply_text(settings_text, parse_mode=enums.ParseMode.HTML)


@app.on_message(filters.command("stats"))
async def stats_command(_, message):
    stats_text = """
<b>📊 إحصائيات البوت:</b>

<b>🤖 البوت يعمل بشكل طبيعي</b>
<b>✅ جميع الميزات متاحة</b>
<b>🔄 جاهز لسحب المنشورات</b>

<b>⚖️ سياسة الاستخدام:</b>
<b>🔐 البوت يعمل ضمن سياسة استخدام تليجرام</b>
<b>✅ يسحب فقط من القنوات والمجموعات العامة</b>
<b>🚫 لا يملك صلاحيات للوصول إلى أي محتوى خاص</b>


"""
    await message.reply_text(stats_text, parse_mode=enums.ParseMode.HTML)


@app.on_message(filters.command("cancel"))
async def cancel_command(_, message):
    user_id = message.from_user.id
    if user_id in active_downloads:
        active_downloads[user_id] = False
        user_states.pop(user_id, None)
        await message.reply_text("<b>✅ تم إلغاء العملية الحالية</b>", parse_mode=enums.ParseMode.HTML)
    else:
        await message.reply_text("<b>❌ لا توجد عملية نشطة للإلغاء</b>", parse_mode=enums.ParseMode.HTML)


# أوامر VIP
@app.on_message(filters.command("vip_info"))
async def vip_info_command(_, message):
    """عرض معلومات VIP للمستخدم"""
    user_id = message.from_user.id
    user_name = message.from_user.first_name

    if auth_manager.is_authorized_user(user_id):
        vip_info = vip_manager.get_vip_info(user_id)

        if vip_info:
            from datetime import datetime
            joined_date = vip_info.get("joined_at", "غير محدد")
            expiry_date = vip_info.get("expires_at", "غير محدد")
            days_total = vip_info.get("days_total", 0)

            # حساب الأيام المتبقية
            try:
                expiry = datetime.strptime(expiry_date, "%Y-%m-%d").date()
                days_left = (expiry - datetime.now().date()).days
                days_left_text = f"{days_left} يوم" if days_left > 0 else "منتهي"
            except:
                days_left_text = "غير محدد"

            message_text = (
                "💎 <b>معلومات اشتراك VIP</b> 💎\n\n"
                f"👤 المستخدم: {user_name}\n"
                f"📅 تاريخ الاشتراك: {joined_date}\n"
                f"⏳ تاريخ الانتهاء: {expiry_date}\n"
                f"⌛ المدة المتبقية: {days_left_text}\n"
                f"📊 مدة الاشتراك: {days_total} يوم\n\n"
                "✅ أنت تتمتع بجميع مميزات VIP!\n"
                "• دعم فني مميز"
            )
        else:
            # في حالة كان المستخدم مشرفًا (VIP تلقائي)
            message_text = (
                "💎 <b>معلومات اشتراك VIP</b> 💎\n\n"
                f"👤 المستخدم: {user_name}\n"
                "🔑 الحالة: مشرف (VIP تلقائي)\n\n"
                "✅ أنت تتمتع بجميع مميزات VIP!\n"
                "• دعم فني مميز"
            )
    else:
        # رسالة للمستخدمين غير VIP
        message_text = (
            "💎 <b>معلومات اشتراك VIP</b> 💎\n\n"
            "❌ أنت غير مشترك في خدمة VIP.\n\n"
            "🚀 <b>استفد من أقوى بوت سحب المنشورات الآن!</b>\n\n"
            "✅ سحب المنشورات بسرعة فائقة\n"
            "✅ دعم فني مميز وسريع\n"
            "✅ أولوية في المعالجة والسحب\n"
            "✅ وفر وقتك وجهدك في سحب المحتوى!\n\n"
            "💸 <b>العرض الحالي:</b>\n"
            "📆 اشتراك لمدة شهر واحد فقط بـ 1 دولار!\n"
            "🎯 استثمر في راحتك بأقل من ثمن كوب شاي يوميًا\n"
            "⚠️ العرض محدود — سارع بالاشتراك الآن!\n\n"
            "📩 للاشتراك والتواصل:\n"
            f"👤 @GurusVIP"
        )

    keyboard = InlineKeyboardMarkup([
        [InlineKeyboardButton("💬 تواصل مع المسؤول", url=VIP_CONTACT_URL)]
    ])

    await message.reply_text(
        message_text,
        reply_markup=keyboard,
        parse_mode=enums.ParseMode.HTML
    )


@app.on_message(filters.command("add_vip"))
async def add_vip_command(_, message):
    """إضافة عضو VIP (للمشرفين فقط)"""
    user_id = message.from_user.id

    # التحقق من صلاحيات المشرف
    if not auth_manager.is_admin(user_id):
        await message.reply_text("⚠️ هذه الميزة متاحة للمشرفين فقط", parse_mode=enums.ParseMode.HTML)
        return

    # استخراج المعاملات
    parts = message.text.split()
    if len(parts) < 3:
        await message.reply_text(
            "❌ يرجى تحديد معرف المستخدم وعدد الأيام\n\n"
            "الاستخدام الصحيح:\n"
            "<code>/add_vip 123456789 30 اسم_المستخدم</code>",
            parse_mode=enums.ParseMode.HTML
        )
        return

    try:
        target_user_id = int(parts[1])
        days = int(parts[2])
        name = " ".join(parts[3:]) if len(parts) > 3 else f"VIP User {target_user_id}"

        # إضافة المستخدم
        if vip_manager.add_vip(target_user_id, name, days):
            from datetime import datetime, timedelta
            expiry_date = (datetime.now() + timedelta(days=days)).strftime("%Y-%m-%d")
            await message.reply_text(
                f"✅ تم إضافة عضو VIP جديد بنجاح!\n\n"
                f"👤 الاسم: {name}\n"
                f"🆔 المعرف: {target_user_id}\n"
                f"⏳ المدة: {days} يوم\n"
                f"📅 تاريخ الانتهاء: {expiry_date}",
                parse_mode=enums.ParseMode.HTML
            )
        else:
            await message.reply_text("❌ فشل في إضافة العضو", parse_mode=enums.ParseMode.HTML)
    except ValueError:
        await message.reply_text("❌ تأكد من صحة المعرف وعدد الأيام", parse_mode=enums.ParseMode.HTML)
    except Exception as e:
        await message.reply_text(f"❌ حدث خطأ غير متوقع: {str(e)}", parse_mode=enums.ParseMode.HTML)


@app.on_message(filters.command("remove_vip"))
async def remove_vip_command(_, message):
    """حذف عضو VIP (للمشرفين فقط)"""
    user_id = message.from_user.id

    # التحقق من صلاحيات المشرف
    if not auth_manager.is_admin(user_id):
        await message.reply_text("⚠️ هذه الميزة متاحة للمشرفين فقط", parse_mode=enums.ParseMode.HTML)
        return

    # استخراج المعاملات
    parts = message.text.split()
    if len(parts) < 2:
        await message.reply_text(
            "❌ يرجى تحديد معرف المستخدم المراد حذفه\n\n"
            "الاستخدام الصحيح:\n"
            "<code>/remove_vip 123456789</code>",
            parse_mode=enums.ParseMode.HTML
        )
        return

    try:
        target_user_id = int(parts[1])

        # الحصول على معلومات المستخدم قبل الحذف
        vip_info = vip_manager.get_vip_info(target_user_id)

        if not vip_info:
            await message.reply_text(
                f"❌ المستخدم غير موجود في قائمة VIP\n"
                f"المعرف: {target_user_id}",
                parse_mode=enums.ParseMode.HTML
            )
            return

        user_name = vip_info.get("name", "غير معروف")

        # حذف المستخدم
        if vip_manager.remove_vip(target_user_id):
            from datetime import datetime
            await message.reply_text(
                f"✅ تم حذف عضو VIP بنجاح!\n\n"
                f"👤 الاسم: {user_name}\n"
                f"🆔 المعرف: {target_user_id}\n"
                f"🗑️ تم الحذف في: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                parse_mode=enums.ParseMode.HTML
            )
        else:
            await message.reply_text("❌ فشل في حذف العضو", parse_mode=enums.ParseMode.HTML)
    except ValueError:
        await message.reply_text("❌ تأكد من صحة معرف المستخدم", parse_mode=enums.ParseMode.HTML)
    except Exception as e:
        await message.reply_text(f"❌ حدث خطأ غير متوقع: {str(e)}", parse_mode=enums.ParseMode.HTML)


@app.on_message(filters.command("extend_vip"))
async def extend_vip_command(_, message):
    """تمديد اشتراك VIP (للمشرفين فقط)"""
    user_id = message.from_user.id

    # التحقق من صلاحيات المشرف
    if not auth_manager.is_admin(user_id):
        await message.reply_text("⚠️ هذه الميزة متاحة للمشرفين فقط", parse_mode=enums.ParseMode.HTML)
        return

    # استخراج المعاملات
    parts = message.text.split()
    if len(parts) < 3:
        await message.reply_text(
            "❌ يرجى تحديد معرف المستخدم وعدد الأيام للتمديد\n\n"
            "الاستخدام الصحيح:\n"
            "<code>/extend_vip 123456789 30</code>",
            parse_mode=enums.ParseMode.HTML
        )
        return

    try:
        target_user_id = int(parts[1])
        additional_days = int(parts[2])

        if additional_days <= 0:
            await message.reply_text("❌ عدد الأيام يجب أن يكون أكبر من صفر", parse_mode=enums.ParseMode.HTML)
            return

        # الحصول على معلومات المستخدم الحالية
        vip_info = vip_manager.get_vip_info(target_user_id)

        if not vip_info:
            await message.reply_text(
                f"❌ المستخدم غير موجود في قائمة VIP\n"
                f"المعرف: {target_user_id}\n\n"
                f"استخدم <code>/add_vip</code> لإضافة عضو جديد",
                parse_mode=enums.ParseMode.HTML
            )
            return

        user_name = vip_info.get("name", "غير معروف")
        current_expiry = vip_info.get("expires_at", "")

        # تمديد الاشتراك
        if vip_manager.extend_vip(target_user_id, additional_days):
            # الحصول على التاريخ الجديد
            updated_info = vip_manager.get_vip_info(target_user_id)
            new_expiry = updated_info.get("expires_at", "")

            await message.reply_text(
                f"✅ تم تمديد اشتراك VIP بنجاح!\n\n"
                f"👤 الاسم: {user_name}\n"
                f"🆔 المعرف: {target_user_id}\n"
                f"📅 التاريخ السابق: {current_expiry}\n"
                f"📅 التاريخ الجديد: {new_expiry}\n"
                f"➕ الأيام المضافة: {additional_days} يوم",
                parse_mode=enums.ParseMode.HTML
            )
        else:
            await message.reply_text("❌ فشل في تمديد الاشتراك", parse_mode=enums.ParseMode.HTML)

    except ValueError:
        await message.reply_text("❌ تأكد من صحة المعرف وعدد الأيام", parse_mode=enums.ParseMode.HTML)
    except Exception as e:
        await message.reply_text(f"❌ حدث خطأ غير متوقع: {str(e)}", parse_mode=enums.ParseMode.HTML)


@app.on_message(filters.command("manage_vip"))
async def manage_vip_command(_, message):
    """إدارة أعضاء VIP (للمشرفين فقط)"""
    user_id = message.from_user.id

    # التحقق من صلاحيات المشرف
    if not auth_manager.is_admin(user_id):
        await message.reply_text("⚠️ هذه الميزة متاحة للمشرفين فقط", parse_mode=enums.ParseMode.HTML)
        return

    # عرض لوحة إدارة VIP
    vip_users = vip_manager.get_all_vip_users()
    stats = vip_manager.get_vip_statistics()

    message_text = "👥 <b>لوحة إدارة أعضاء VIP</b>\n\n"

    if vip_users:
        message_text += f"<b>📊 الإحصائيات:</b>\n"
        message_text += f"• إجمالي الأعضاء: {stats['total']}\n"
        message_text += f"• الأعضاء النشطين: {stats['active']}\n"
        message_text += f"• الاشتراكات المنتهية: {stats['expired']}\n"
        message_text += f"• ينتهي قريباً: {stats['expiring_soon']}\n\n"

        message_text += "<b>👥 قائمة الأعضاء:</b>\n"
        for _, user_info in list(vip_users.items())[:10]:  # أول 10 أعضاء فقط
            name = user_info.get("name", "غير معروف")[:20]
            expiry_date = user_info.get("expires_at", "غير محدد")

            # تحديد حالة العضوية
            try:
                from datetime import datetime
                expiry = datetime.strptime(expiry_date, "%Y-%m-%d").date()
                current_date = datetime.now().date()

                if expiry >= current_date:
                    days_left = (expiry - current_date).days
                    if days_left > 7:
                        status = "نشط ✅"
                    else:
                        status = f"ينتهي قريباً ⚠️"
                else:
                    status = "منتهي ❌"
            except:
                status = "خطأ ⚠️"

            message_text += f"• {name} - {status}\n"

        if len(vip_users) > 10:
            message_text += f"... و {len(vip_users) - 10} عضو آخر\n"
    else:
        message_text += "<i>لا يوجد أعضاء VIP حالياً.</i>\n"

    message_text += "\n<b>🛠️ أوامر الإدارة:</b>\n"
    message_text += "• <code>/add_vip [ID] [أيام] [اسم]</code> - إضافة عضو\n"
    message_text += "• <code>/remove_vip [ID]</code> - حذف عضو\n"
    message_text += "• <code>/extend_vip [ID] [أيام]</code> - تمديد اشتراك\n"
    message_text += "• <code>/clean_vip</code> - تنظيف المنتهية"

    await message.reply_text(message_text, parse_mode=enums.ParseMode.HTML)


@app.on_message(filters.command("clean_vip"))
async def clean_vip_command(_, message):
    """تنظيف أعضاء VIP المنتهية الصلاحية (للمشرفين فقط)"""
    user_id = message.from_user.id

    # التحقق من صلاحيات المشرف
    if not auth_manager.is_admin(user_id):
        await message.reply_text("⚠️ هذه الميزة متاحة للمشرفين فقط", parse_mode=enums.ParseMode.HTML)
        return

    # إرسال رسالة التحميل
    status_msg = await message.reply_text("🧹 جاري تنظيف أعضاء VIP المنتهية الصلاحية...", parse_mode=enums.ParseMode.HTML)

    try:
        # تنظيف الأعضاء المنتهية الصلاحية
        removed_count, removed_names = vip_manager.clean_expired_vip()

        if removed_count > 0:
            result_message = f"✅ تم حذف {removed_count} عضو VIP منتهي الصلاحية:\n\n"
            result_message += "\n".join(removed_names[:10])  # عرض أول 10 أعضاء فقط
            if len(removed_names) > 10:
                result_message += f"\n... و {len(removed_names) - 10} عضو آخر"
        else:
            result_message = "✅ لا يوجد أعضاء VIP منتهية الصلاحية للحذف"

        await status_msg.edit_text(result_message, parse_mode=enums.ParseMode.HTML)

    except Exception:
        await status_msg.edit_text("❌ حدث خطأ أثناء تنظيف أعضاء VIP", parse_mode=enums.ParseMode.HTML)


@app.on_message(filters.command("toggle_vip"))
async def toggle_vip_command(_, message):
    """تبديل نظام VIP بين المجاني والمدفوع (للمشرفين فقط)"""
    user_id = message.from_user.id

    # التحقق من صلاحيات المشرف
    if not auth_manager.is_admin(user_id):
        await message.reply_text("⚠️ هذه الميزة متاحة للمشرفين فقط", parse_mode=enums.ParseMode.HTML)
        return

    # تبديل حالة نظام VIP
    new_status = toggle_vip_system()
    status_text = "🟢 مفعل" if new_status else "🔴 معطل"
    mode_text = "المدفوع" if new_status else "المجاني"

    message_text = f"""
<b>✅ تم تبديل نظام VIP بنجاح!</b>

<b>📊 الحالة الحالية:</b>
<b>• نظام VIP: {status_text}</b>
<b>• الوضع: {mode_text}</b>

<b>📝 التفاصيل:</b>
"""

    if new_status:
        message_text += """<b>• الميزات المتقدمة تتطلب اشتراك VIP</b>
<b>• سحب منشور واحد متاح للجميع مجاناً</b>
<b>• سحب النطاق والتسلسلي للأعضاء المميزين فقط</b>"""
    else:
        message_text += """<b>• جميع الميزات متاحة مجاناً للجميع</b>
<b>• لا يوجد قيود على الاستخدام</b>
<b>• جميع أنواع السحب متاحة</b>"""

    await message.reply_text(message_text, parse_mode=enums.ParseMode.HTML)


@app.on_message(filters.command("add_welcome_image"))
async def add_welcome_image_command(_, message):
    """إضافة صورة جديدة لقائمة صور الترحيب (للمشرفين فقط)"""
    user_id = message.from_user.id

    # التحقق من صلاحيات المشرف
    if not auth_manager.is_admin(user_id):
        await message.reply_text("⚠️ هذه الميزة متاحة للمشرفين فقط", parse_mode=enums.ParseMode.HTML)
        return

    # استخراج رابط الصورة
    parts = message.text.split()
    if len(parts) < 2:
        current_images = "\n".join([f"• {img}" for img in WELCOME_IMAGES])
        await message.reply_text(
            f"❌ يرجى تحديد رابط الصورة\n\n"
            f"<b>الاستخدام الصحيح:</b>\n"
            f"<code>/add_welcome_image https://example.com/image.jpg</code>\n\n"
            f"<b>📸 الصور الحالية ({len(WELCOME_IMAGES)}):</b>\n{current_images}",
            parse_mode=enums.ParseMode.HTML
        )
        return

    image_url = parts[1]

    # التحقق من صحة الرابط
    if not image_url.startswith(('http://', 'https://')):
        await message.reply_text("❌ رابط الصورة غير صحيح. يجب أن يبدأ بـ http:// أو https://", parse_mode=enums.ParseMode.HTML)
        return

    # التحقق من أن الرابط ليس موجود مسبقاً
    if image_url in WELCOME_IMAGES:
        await message.reply_text("⚠️ هذه الصورة موجودة بالفعل في القائمة", parse_mode=enums.ParseMode.HTML)
        return

    # إضافة الصورة للقائمة
    WELCOME_IMAGES.append(image_url)

    await message.reply_text(
        f"✅ تم إضافة الصورة بنجاح!\n\n"
        f"🔗 الرابط: {image_url}\n"
        f"📊 إجمالي الصور: {len(WELCOME_IMAGES)} صورة\n\n"
        f"💡 ستظهر هذه الصورة عشوائياً مع الصور الأخرى عند استخدام /start",
        parse_mode=enums.ParseMode.HTML
    )


@app.on_message(filters.command("list_welcome_images"))
async def list_welcome_images_command(_, message):
    """عرض قائمة صور الترحيب (للمشرفين فقط)"""
    user_id = message.from_user.id

    # التحقق من صلاحيات المشرف
    if not auth_manager.is_admin(user_id):
        await message.reply_text("⚠️ هذه الميزة متاحة للمشرفين فقط", parse_mode=enums.ParseMode.HTML)
        return

    if not WELCOME_IMAGES:
        await message.reply_text("📭 لا توجد صور في قائمة الترحيب", parse_mode=enums.ParseMode.HTML)
        return

    images_list = ""
    for i, img in enumerate(WELCOME_IMAGES, 1):
        images_list += f"{i}. {img}\n"

    await message.reply_text(
        f"📸 <b>قائمة صور الترحيب ({len(WELCOME_IMAGES)} صورة):</b>\n\n"
        f"{images_list}\n"
        f"💡 يتم اختيار صورة عشوائية من هذه القائمة عند كل استخدام لأمر /start",
        parse_mode=enums.ParseMode.HTML
    )


@app.on_message(filters.command("test_random_image"))
async def test_random_image_command(_, message):
    """اختبار الصور العشوائية (للمشرفين فقط)"""
    user_id = message.from_user.id

    # التحقق من صلاحيات المشرف
    if not auth_manager.is_admin(user_id):
        await message.reply_text("⚠️ هذه الميزة متاحة للمشرفين فقط", parse_mode=enums.ParseMode.HTML)
        return

    if not WELCOME_IMAGES:
        await message.reply_text("📭 لا توجد صور في قائمة الترحيب", parse_mode=enums.ParseMode.HTML)
        return

    # اختيار صورة عشوائية
    random_image = get_next_welcome_image()

    try:
        await message.reply_photo(
            photo=random_image,
            caption=f"🎲 <b>اختبار الصورة العشوائية</b>\n\n"
                   f"📸 الصورة المختارة: {WELCOME_IMAGES.index(random_image) + 1} من {len(WELCOME_IMAGES)}\n"
                   f"🔗 الرابط: {random_image}",
            parse_mode=enums.ParseMode.HTML
        )
    except Exception as e:
        await message.reply_text(
            f"❌ فشل في تحميل الصورة\n\n"
            f"🔗 الرابط: {random_image}\n"
            f"⚠️ الخطأ: {str(e)}",
            parse_mode=enums.ParseMode.HTML
        )


@app.on_message(filters.command("reset_images"))
async def reset_images_command(_, message):
    """إعادة تعيين قائمة الصور المستخدمة (للمشرفين فقط)"""
    user_id = message.from_user.id

    # التحقق من صلاحيات المشرف
    if not auth_manager.is_admin(user_id):
        await message.reply_text("⚠️ هذه الميزة متاحة للمشرفين فقط", parse_mode=enums.ParseMode.HTML)
        return

    global used_images, current_image_index
    used_images = []
    current_image_index = 0

    await message.reply_text(
        "✅ <b>تم إعادة تعيين قائمة الصور بنجاح!</b>\n\n"
        f"📸 إجمالي الصور: {len(WELCOME_IMAGES)} صورة\n"
        f"🔄 الصور المستخدمة: {len(used_images)} صورة\n\n"
        f"💡 ستبدأ الصور من جديد مع أول /start",
        parse_mode=enums.ParseMode.HTML
    )


@app.on_message(filters.command("images_status"))
async def images_status_command(_, message):
    """عرض حالة الصور المستخدمة (للمشرفين فقط)"""
    user_id = message.from_user.id

    # التحقق من صلاحيات المشرف
    if not auth_manager.is_admin(user_id):
        await message.reply_text("⚠️ هذه الميزة متاحة للمشرفين فقط", parse_mode=enums.ParseMode.HTML)
        return

    global used_images, current_image_index

    unused_images = [img for img in WELCOME_IMAGES if img not in used_images]

    status_text = f"📊 <b>حالة صور الترحيب:</b>\n\n"
    status_text += f"📸 إجمالي الصور: {len(WELCOME_IMAGES)} صورة\n"
    status_text += f"✅ الصور المستخدمة: {len(used_images)} صورة\n"
    status_text += f"⏳ الصور المتبقية: {len(unused_images)} صورة\n\n"

    if used_images:
        status_text += f"<b>🔸 الصور المستخدمة:</b>\n"
        for i, img in enumerate(used_images, 1):
            img_name = img.split('/')[-1][:20] + "..."
            status_text += f"{i}. {img_name}\n"
        status_text += "\n"

    if unused_images:
        status_text += f"<b>🔹 الصور المتبقية:</b>\n"
        for i, img in enumerate(unused_images, 1):
            img_name = img.split('/')[-1][:20] + "..."
            status_text += f"{i}. {img_name}\n"
    else:
        status_text += f"<b>🔄 جميع الصور مستخدمة - ستبدأ من جديد مع /start التالي</b>\n"

    await message.reply_text(status_text, parse_mode=enums.ParseMode.HTML)


@app.on_message(filters.command("check_vip_notifications"))
async def check_vip_notifications_command(_, message):
    """فحص وإرسال إشعارات انتهاء VIP يدوياً (للمشرفين فقط)"""
    user_id = message.from_user.id

    # التحقق من صلاحيات المشرف
    if not auth_manager.is_admin(user_id):
        await message.reply_text("⚠️ هذه الميزة متاحة للمشرفين فقط", parse_mode=enums.ParseMode.HTML)
        return

    status_msg = await message.reply_text(
        "<b>🔍 جاري فحص إشعارات انتهاء VIP...</b>",
        parse_mode=enums.ParseMode.HTML
    )

    try:
        # تشغيل فحص الإشعارات
        await check_vip_expiry_notifications()

        # عرض إحصائيات
        all_vip_users = vip_manager.get_all_vip_users()
        today = datetime.now().date()

        expiring_soon = 0
        expiring_today = 0

        for user_data in all_vip_users.values():
            try:
                expires_at = datetime.strptime(user_data['expires_at'], "%Y-%m-%d").date()
                days_remaining = (expires_at - today).days

                if days_remaining == 3:
                    expiring_soon += 1
                elif days_remaining == 0:
                    expiring_today += 1
            except:
                continue

        result_message = f"""
<b>✅ تم فحص إشعارات انتهاء VIP</b>

<b>📊 الإحصائيات:</b>
<b>• إجمالي أعضاء VIP: {len(all_vip_users)}</b>
<b>• ينتهي خلال 3 أيام: {expiring_soon}</b>
<b>• ينتهي اليوم: {expiring_today}</b>
<b>• إشعارات مرسلة: {len(sent_notifications)}</b>

<b>📅 آخر فحص: {datetime.now().strftime('%Y-%m-%d %H:%M')}</b>
"""

        await status_msg.edit_text(result_message, parse_mode=enums.ParseMode.HTML)

    except Exception as e:
        await status_msg.edit_text(
            f"<b>❌ حدث خطأ أثناء فحص الإشعارات</b>\n\n<code>{str(e)}</code>",
            parse_mode=enums.ParseMode.HTML
        )


@app.on_message(filters.command("test_vip_notification"))
async def test_vip_notification_command(_, message):
    """اختبار إرسال إشعار VIP (للمشرفين فقط)"""
    user_id = message.from_user.id

    # التحقق من صلاحيات المشرف
    if not auth_manager.is_admin(user_id):
        await message.reply_text("⚠️ هذه الميزة متاحة للمشرفين فقط", parse_mode=enums.ParseMode.HTML)
        return

    # إرسال إشعار تجريبي للمشرف نفسه
    test_user_data = {
        'name': 'مشرف تجريبي',
        'expires_at': (datetime.now().date() + timedelta(days=3)).strftime('%Y-%m-%d')
    }

    try:
        await send_vip_expiry_notification(user_id, test_user_data, datetime.now().date() + timedelta(days=3))
        await message.reply_text(
            "<b>✅ تم إرسال إشعار تجريبي بنجاح!</b>\n\n"
            "<b>📱 تحقق من الرسائل الخاصة</b>",
            parse_mode=enums.ParseMode.HTML
        )
    except Exception as e:
        await message.reply_text(
            f"<b>❌ فشل إرسال الإشعار التجريبي</b>\n\n<code>{str(e)}</code>",
            parse_mode=enums.ParseMode.HTML
        )


@app.on_message(filters.command("test_progress"))
async def test_progress_command(_, message):
    """اختبار أشرطة التقدم المختلفة (للمشرفين فقط)"""
    user_id = message.from_user.id

    # التحقق من صلاحيات المشرف
    if not auth_manager.is_admin(user_id):
        await message.reply_text("⚠️ هذه الميزة متاحة للمشرفين فقط", parse_mode=enums.ParseMode.HTML)
        return

    # اختبار أشرطة التقدم المختلفة
    test_message = "<b>🧪 اختبار أشرطة التقدم:</b>\n\n"

    # شريط تقدم أساسي
    test_message += "<b>📊 شريط التقدم الأساسي:</b>\n"
    for i in [0, 25, 50, 75, 100]:
        bar = create_progress_bar(i, 100, 15)
        test_message += f"{bar}\n"

    test_message += "\n<b>🎨 شريط التقدم المتحرك:</b>\n"
    for i in [0, 30, 60, 90, 100]:
        bar = create_animated_progress_bar(i, 100, 12)
        test_message += f"{bar}\n"

    test_message += "\n<b>📈 معلومات مفصلة:</b>\n"
    detailed_info = create_detailed_progress_info(75, 100, 70, 5)
    test_message += detailed_info

    await message.reply_text(test_message, parse_mode=enums.ParseMode.HTML)


@app.on_message(filters.command("test_vip_expiry"))
async def test_vip_expiry_command(_, message):
    """اختبار نظام انتهاء العضوية (للمشرفين فقط)"""
    user_id = message.from_user.id

    # التحقق من صلاحيات المشرف
    if not auth_manager.is_admin(user_id):
        await message.reply_text("⚠️ هذه الميزة متاحة للمشرفين فقط", parse_mode=enums.ParseMode.HTML)
        return

    await message.reply_text("🧪 <b>بدء اختبار نظام انتهاء العضوية...</b>", parse_mode=enums.ParseMode.HTML)

    # إضافة عضو تجريبي لمدة يوم واحد فقط
    test_user_id = 999999999
    test_user_name = "اختبار انتهاء العضوية"

    # إضافة عضو لمدة يوم واحد
    result = vip_manager.add_vip(test_user_id, test_user_name, 1)
    if result:
        await message.reply_text(
            f"✅ <b>تم إضافة عضو تجريبي:</b>\n"
            f"👤 الاسم: {test_user_name}\n"
            f"🆔 المعرف: {test_user_id}\n"
            f"⏰ المدة: يوم واحد",
            parse_mode=enums.ParseMode.HTML
        )

        # التحقق من أنه VIP الآن
        is_vip_now = vip_manager.is_vip(test_user_id)
        await message.reply_text(f"🔍 <b>هل هو VIP الآن؟</b> {'✅ نعم' if is_vip_now else '❌ لا'}", parse_mode=enums.ParseMode.HTML)

        # عرض معلومات العضو
        vip_info = vip_manager.get_vip_info(test_user_id)
        if vip_info:
            await message.reply_text(f"📅 <b>تاريخ الانتهاء:</b> {vip_info.get('expires_at')}", parse_mode=enums.ParseMode.HTML)

        # محاكاة انتهاء العضوية (تعديل التاريخ للأمس)
        from datetime import datetime, timedelta
        yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")

        # تعديل تاريخ الانتهاء للأمس
        vip_data = vip_manager.get_all_vip_users()
        if str(test_user_id) in vip_data:
            vip_data[str(test_user_id)]['expires_at'] = yesterday
            vip_manager.vip_data = vip_data
            vip_manager._save_vip_data()
            await message.reply_text(f"🕐 <b>تم تعديل تاريخ الانتهاء إلى:</b> {yesterday}", parse_mode=enums.ParseMode.HTML)

            # التحقق من أنه لم يعد VIP
            is_vip_after_expiry = vip_manager.is_vip(test_user_id)
            await message.reply_text(f"🔍 <b>هل هو VIP بعد انتهاء العضوية؟</b> {'❌ لا' if not is_vip_after_expiry else '✅ نعم'}", parse_mode=enums.ParseMode.HTML)

            if not is_vip_after_expiry:
                await message.reply_text("✅ <b>نظام انتهاء العضوية يعمل بشكل صحيح!</b>", parse_mode=enums.ParseMode.HTML)

                # اختبار التنظيف التلقائي
                await message.reply_text("🧹 <b>اختبار التنظيف التلقائي...</b>", parse_mode=enums.ParseMode.HTML)
                removed_count, removed_names = vip_manager.clean_expired_vip()

                if removed_count > 0:
                    result_text = f"✅ <b>تم حذف {removed_count} عضو منتهي الصلاحية:</b>\n"
                    for name in removed_names:
                        result_text += f"   - {name}\n"
                    result_text += "\n✅ <b>نظام التنظيف التلقائي يعمل بشكل صحيح!</b>"
                    await message.reply_text(result_text, parse_mode=enums.ParseMode.HTML)
                else:
                    await message.reply_text("❌ <b>لم يتم حذف أي عضو منتهي الصلاحية</b>", parse_mode=enums.ParseMode.HTML)
            else:
                await message.reply_text("❌ <b>نظام انتهاء العضوية لا يعمل بشكل صحيح!</b>", parse_mode=enums.ParseMode.HTML)
        else:
            await message.reply_text("❌ <b>لم يتم العثور على العضو التجريبي</b>", parse_mode=enums.ParseMode.HTML)
    else:
        await message.reply_text("❌ <b>فشل في إضافة العضو التجريبي</b>", parse_mode=enums.ParseMode.HTML)


@app.on_message(filters.command("admin_logs") & filters.user(list(ADMIN_IDS)) if ADMIN_IDS else filters.user([]))
async def admin_logs_command(_, message):
    """عرض إحصائيات الروابط للمشرف فقط"""
    try:
        with open(LINKS_LOG_FILE, 'r', encoding='utf-8') as f:
            content = f.read()

        # استخراج البيانات من ملف JS
        start_marker = "/*JSON_START*/"
        end_marker = "/*JSON_END*/"
        start = content.find(start_marker)
        end = content.find(end_marker)

        if start != -1 and end != -1:
            start += len(start_marker)
            json_data = content[start:end].strip()
            links_data = json.loads(json_data)
        else:
            # إذا لم توجد العلامات، جرب الطريقة القديمة
            start = content.find('[')
            end = content.rfind(']') + 1
            if start != -1 and end != 0:
                json_data = content[start:end]
                links_data = json.loads(json_data)
            else:
                links_data = []

        if not links_data:
            await message.reply_text("<b>📊 لا توجد روابط مسجلة بعد</b>", parse_mode=enums.ParseMode.HTML)
            return

        # إحصائيات
        total_links = len(links_data)
        unique_users = len(set(item['user_id'] for item in links_data))
        unique_channels = len(set(item['channel'] for item in links_data if item['channel'] != 'غير معروف'))

        # آخر 10 روابط
        recent_links = links_data[-10:]

        admin_text = f"""
<b>🔍 إحصائيات الروابط المسجلة</b>

<b>📊 الإحصائيات العامة:</b>
<b>🔗 إجمالي الروابط: {total_links}</b>
<b>👥 عدد المستخدمين: {unique_users}</b>
<b>📺 عدد القنوات: {unique_channels}</b>

<b>📝 آخر 10 روابط:</b>
"""

        for link in recent_links:
            channel = link['channel'][:15] + "..." if len(link['channel']) > 15 else link['channel']
            username = link['username'][:10] + "..." if len(link['username']) > 10 else link['username']
            admin_text += f"<b>• {username} - {channel} - {link['timestamp'].split()[1][:5]}</b>\n"

        admin_text += f"\n<b>📁 الملف الكامل: {LINKS_LOG_FILE}</b>"

        await message.reply_text(admin_text, parse_mode=enums.ParseMode.HTML)

    except Exception as e:
        await message.reply_text(f"<b>❌ خطأ في قراءة السجلات: {str(e)}</b>", parse_mode=enums.ParseMode.HTML)


@app.on_message(filters.command("exclude_link") & filters.user(list(ADMIN_IDS)) if ADMIN_IDS else filters.user([]))
async def exclude_link_command(_, message):
    """إضافة رابط للقائمة المستثناة"""
    try:
        # استخراج الرابط من الرسالة
        parts = message.text.split(maxsplit=1)
        if len(parts) < 2:
            await message.reply_text("<b>❌ الاستخدام: /exclude_link [الرابط]</b>", parse_mode=enums.ParseMode.HTML)
            return

        link = parts[1].strip()

        # إضافة للقائمة المستثناة
        if link not in EXCLUDED_LINKS:
            EXCLUDED_LINKS.append(link)
            await message.reply_text(f"<b>✅ تم إضافة الرابط للقائمة المستثناة:</b>\n<code>{link}</code>", parse_mode=enums.ParseMode.HTML)
        else:
            await message.reply_text(f"<b>⚠️ الرابط موجود بالفعل في القائمة المستثناة:</b>\n<code>{link}</code>", parse_mode=enums.ParseMode.HTML)

    except Exception as e:
        await message.reply_text(f"<b>❌ خطأ: {str(e)}</b>", parse_mode=enums.ParseMode.HTML)


@app.on_message(filters.command("exclude_channel") & filters.user(list(ADMIN_IDS)) if ADMIN_IDS else filters.user([]))
async def exclude_channel_command(_, message):
    """إضافة قناة للقائمة المستثناة"""
    try:
        # استخراج اسم القناة من الرسالة
        parts = message.text.split(maxsplit=1)
        if len(parts) < 2:
            await message.reply_text("<b>❌ الاستخدام: /exclude_channel [اسم_القناة]</b>", parse_mode=enums.ParseMode.HTML)
            return

        channel = parts[1].strip()

        # إضافة للقائمة المستثناة
        if channel not in EXCLUDED_CHANNELS:
            EXCLUDED_CHANNELS.append(channel)
            await message.reply_text(f"<b>✅ تم إضافة القناة للقائمة المستثناة:</b>\n<code>{channel}</code>", parse_mode=enums.ParseMode.HTML)
        else:
            await message.reply_text(f"<b>⚠️ القناة موجودة بالفعل في القائمة المستثناة:</b>\n<code>{channel}</code>", parse_mode=enums.ParseMode.HTML)

    except Exception as e:
        await message.reply_text(f"<b>❌ خطأ: {str(e)}</b>", parse_mode=enums.ParseMode.HTML)


@app.on_message(filters.command("show_excluded") & filters.user(list(ADMIN_IDS)) if ADMIN_IDS else filters.user([]))
async def show_excluded_command(_, message):
    """عرض القوائم المستثناة"""
    excluded_text = """
**🚫 القوائم المستثناة من التسجيل:**

**🔗 الروابط المستثناة:**
"""

    if EXCLUDED_LINKS:
        for i, link in enumerate(EXCLUDED_LINKS, 1):
            excluded_text += f"**{i}. `{link}`**\n"
    else:
        excluded_text += "**لا توجد روابط مستثناة**\n"

    excluded_text += "\n**📺 القنوات المستثناة:**\n"

    if EXCLUDED_CHANNELS:
        for i, channel in enumerate(EXCLUDED_CHANNELS, 1):
            excluded_text += f"**{i}. `{channel}`**\n"
    else:
        excluded_text += "**لا توجد قنوات مستثناة**\n"

    excluded_text += """
**📝 أوامر الإدارة:**
**/exclude_link [رابط] - إضافة رابط للاستثناء**
**/exclude_channel [قناة] - إضافة قناة للاستثناء**
**/show_excluded - عرض القوائم المستثناة**
"""

    await message.reply_text(excluded_text, parse_mode=enums.ParseMode.MARKDOWN)


@app.on_message(filters.command("test_format"))
async def test_format_command(_, message):
    """اختبار تنسيقات مختلفة للنص"""

    # اختبار HTML
    try:
        html_text = """<b>🧪 اختبار تنسيق HTML</b>

<b>✅ هذا نص عريض بـ HTML</b>
<i>هذا نص مائل</i>
<u>هذا نص مسطر</u>
<code>هذا كود</code>

<b>📝 قائمة:</b>
<b>• العنصر الأول</b>
<b>• العنصر الثاني</b>
<b>• العنصر الثالث</b>"""

        await message.reply_text(html_text, parse_mode=enums.ParseMode.HTML)
        await message.reply_text("✅ تم إرسال اختبار HTML بنجاح")
    except Exception as e:
        await message.reply_text(f"❌ فشل HTML: {str(e)}")

    # اختبار Markdown
    try:
        markdown_text = """**🧪 اختبار تنسيق Markdown**

**✅ هذا نص عريض بـ Markdown**
*هذا نص مائل*
`هذا كود`

**📝 قائمة:**
**• العنصر الأول**
**• العنصر الثاني**
**• العنصر الثالث**"""

        await message.reply_text(markdown_text, parse_mode=enums.ParseMode.MARKDOWN)
        await message.reply_text("✅ تم إرسال اختبار Markdown بنجاح")
    except Exception as e:
        await message.reply_text(f"❌ فشل Markdown: {str(e)}")

    # اختبار تنسيق مختلط
    try:
        mixed_text = """🧪 اختبار تنسيق مختلط

**✅ هذا نص عريض**
*هذا نص مائل*
`هذا كود`
__هذا نص مسطر__

**📝 قائمة:**
**• العنصر الأول**
**• العنصر الثاني**
**• العنصر الثالث**"""

        await message.reply_text(mixed_text, parse_mode=enums.ParseMode.MARKDOWN)
        await message.reply_text("✅ تم إرسال اختبار التنسيق المختلط بنجاح")
    except Exception as e:
        await message.reply_text(f"❌ فشل التنسيق المختلط: {str(e)}")

    # اختبار بدون تنسيق
    plain_text = """🧪 اختبار بدون تنسيق

هذا نص عادي بدون أي تنسيق
لا يوجد نص عريض أو مائل
فقط نص عادي"""

    await message.reply_text(plain_text)
    await message.reply_text("✅ تم إرسال النص العادي")


@app.on_message(filters.command("bold_mode"))
async def bold_mode_command(_, message):
    """معلومات حول وضع النص العريض الجديد"""

    info_text = """<b>🎨 وضع النص العريض الجديد</b>

<b>✨ الميزات الجديدة:</b>
• <b>تحويل النص العادي إلى نص عريض تلقائياً</b>
• <b>الحفاظ على التنسيق الموجود مسبقاً</b>
• <b>دعم جميع أنواع الوسائط مع النص العريض</b>
• <b>معالجة ذكية للنصوص المختلطة</b>

<b>� كيف يعمل:</b>
• النص العادي → <b>نص عريض</b>
• النص المنسق مسبقاً → يبقى كما هو
• الوسائط + نص → وسائط + <b>نص عريض</b>

<b>📝 أمثلة:</b>
• "مرحبا" → <b>"مرحبا"</b>
• "<b>مرحبا</b>" → <b>"مرحبا"</b> (لا يتغير)
• "**مرحبا**" → <b>"مرحبا"</b> (تحويل Markdown)

<b>🎯 النتيجة:</b>
جميع المنشورات المسحوبة ستظهر بخط عريض وواضح!"""

    await message.reply_text(info_text, parse_mode=enums.ParseMode.HTML)


@app.on_message(filters.command("test_bold"))
async def test_bold_command(_, message):
    """اختبار وضع النص العريض"""

    # اختبار النص العادي
    normal_text = "هذا نص عادي سيصبح عريضاً"
    bold_result = await make_text_bold(normal_text)
    await message.reply_text(f"النص الأصلي: {normal_text}")
    await message.reply_text(f"النتيجة: {bold_result}", parse_mode=enums.ParseMode.HTML)

    # اختبار النص المنسق مسبقاً
    formatted_text = "<b>هذا نص عريض مسبقاً</b>"
    formatted_result = await make_text_bold(formatted_text)
    await message.reply_text(f"النص المنسق: {formatted_text}", parse_mode=enums.ParseMode.HTML)
    await message.reply_text(f"النتيجة: {formatted_result}", parse_mode=enums.ParseMode.HTML)

    # اختبار Markdown
    markdown_text = "**هذا نص Markdown**"
    markdown_result = await make_text_bold(markdown_text)
    await message.reply_text(f"نص Markdown: {markdown_text}")
    await message.reply_text(f"النتيجة: {markdown_result}", parse_mode=enums.ParseMode.HTML)


async def show_vip_management_table(callback, page=0):
    """عرض جدول إدارة VIP مع أزرار حذف لكل عضو"""
    vip_users = vip_manager.get_all_vip_users()
    stats = vip_manager.get_vip_statistics()

    # إعدادات الصفحات
    users_per_page = 5
    total_users = len(vip_users)
    total_pages = (total_users + users_per_page - 1) // users_per_page if total_users > 0 else 1

    # التأكد من أن رقم الصفحة صحيح
    if page < 0:
        page = 0
    elif page >= total_pages:
        page = total_pages - 1

    # حساب نطاق المستخدمين للصفحة الحالية
    start_idx = page * users_per_page
    end_idx = min(start_idx + users_per_page, total_users)

    # إنشاء رسالة الجدول
    message_text = "👥 <b>لوحة إدارة أعضاء VIP</b>\n\n"

    # الإحصائيات
    message_text += f"<b>📊 الإحصائيات العامة:</b>\n"
    message_text += f"• إجمالي الأعضاء: {stats['total']}\n"
    message_text += f"• الأعضاء النشطين: {stats['active']}\n"
    message_text += f"• الاشتراكات المنتهية: {stats['expired']}\n"
    message_text += f"• ينتهي قريباً: {stats['expiring_soon']}\n\n"

    if vip_users:
        message_text += f"<b>📋 جدول الأعضاء (صفحة {page + 1}/{total_pages}):</b>\n"
        message_text += "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n"

        # عرض المستخدمين في الصفحة الحالية
        users_list = list(vip_users.items())[start_idx:end_idx]

        for i, (user_id_str, user_info) in enumerate(users_list, 1):
            name = user_info.get("name", "غير معروف")[:15]
            expiry_date = user_info.get("expires_at", "غير محدد")
            joined_date = user_info.get("joined_at", "غير محدد")

            # تحديد حالة العضوية
            try:
                from datetime import datetime
                expiry = datetime.strptime(expiry_date, "%Y-%m-%d").date()
                current_date = datetime.now().date()

                if expiry >= current_date:
                    days_left = (expiry - current_date).days
                    if days_left > 7:
                        status = "🟢 نشط"
                        status_detail = f"({days_left} يوم)"
                    else:
                        status = "🟡 ينتهي قريباً"
                        status_detail = f"({days_left} يوم)"
                else:
                    status = "🔴 منتهي"
                    days_expired = (current_date - expiry).days
                    status_detail = f"(منذ {days_expired} يوم)"
            except:
                status = "⚠️ خطأ"
                status_detail = ""

            # تنسيق معلومات العضو مع زر الحذف في نفس السطر
            message_text += f"<b>{start_idx + i}.</b> <code>{name}</code> | {status} {status_detail}\n"
            message_text += f"   🆔 <code>{user_id_str}</code> | 📅 {joined_date} | ⏰ {expiry_date}\n"
            message_text += "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n"

    else:
        message_text += "<i>📭 لا يوجد أعضاء VIP حالياً.</i>\n"

    # إنشاء لوحة المفاتيح
    keyboard_buttons = []

    # أزرار حذف الأعضاء - زر لكل عضو في صف منفصل
    if vip_users and users_list:
        for i, (user_id_str, user_info) in enumerate(users_list, 1):
            name = user_info.get("name", "غير معروف")[:12]

            # تحديد حالة العضوية للزر
            try:
                from datetime import datetime
                expiry_date = user_info.get("expires_at", "غير محدد")
                expiry = datetime.strptime(expiry_date, "%Y-%m-%d").date()
                current_date = datetime.now().date()

                if expiry >= current_date:
                    days_left = (expiry - current_date).days
                    if days_left > 7:
                        status_icon = "🟢"
                    else:
                        status_icon = "🟡"
                else:
                    status_icon = "🔴"
            except:
                status_icon = "⚠️"

            # إنشاء صف لكل عضو: اسم العضو + زر الحذف
            member_row = [
                InlineKeyboardButton(f"{status_icon} {name}", callback_data="vip_info_display"),
                InlineKeyboardButton("🗑️ حذف", callback_data=f"delete_vip_{user_id_str}")
            ]
            keyboard_buttons.append(member_row)

        # إضافة فاصل بين قائمة الأعضاء وأزرار التحكم
        keyboard_buttons.append([InlineKeyboardButton("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━", callback_data="vip_info_display")])

    # أزرار التنقل بين الصفحات
    nav_buttons = []
    if total_pages > 1:
        if page > 0:
            nav_buttons.append(InlineKeyboardButton("⬅️ السابق", callback_data=f"vip_page_{page-1}"))

        nav_buttons.append(InlineKeyboardButton(f"📄 {page + 1}/{total_pages}", callback_data="vip_info_display"))

        if page < total_pages - 1:
            nav_buttons.append(InlineKeyboardButton("➡️ التالي", callback_data=f"vip_page_{page+1}"))

        keyboard_buttons.append(nav_buttons)

    # أزرار الإدارة
    keyboard_buttons.append([
        InlineKeyboardButton("🔄 تحديث", callback_data="manage_vip"),
        InlineKeyboardButton("🧹 تنظيف المنتهية", callback_data="clean_expired_vip")
    ])

    keyboard_buttons.append([
        InlineKeyboardButton("➕ إضافة عضو", callback_data="add_vip_guide"),
        InlineKeyboardButton("🔙 العودة", callback_data="back_to_menu")
    ])

    keyboard = InlineKeyboardMarkup(keyboard_buttons)

    await safe_edit_message(callback.message, message_text, reply_markup=keyboard)


async def show_vip_required_message(callback):
    """عرض رسالة تطلب الاشتراك في VIP للميزات المتقدمة"""
    vip_message = (
        "<b>🔒 ميزة VIP مطلوبة!</b>\n\n"
        "<b>📋 هذه الميزة متاحة لأعضاء VIP فقط:</b>\n\n"

        "<b>🆓 الميزات المجانية المتاحة:</b>\n"
        "<b>✅ سحب منشور واحد</b>\n\n"

        "<b>💎 ميزات VIP الحصرية:</b>\n"
        "<b>🚀 سحب نطاق محدد من المنشورات</b>\n"
        "<b>⬆️ سحب تسلسلي للخلف</b>\n"
        "<b>⬇️ سحب تسلسلي للأمام</b>\n"
        "<b>🎯 أولوية في المعالجة والسحب</b>\n\n"

        "<b>💰 اشترك الآن مقابل 1$ شهرياً فقط!</b>\n"
        "<b>🎁 تجربة مجانية للمشتركين الجدد</b>"
    )

    keyboard = InlineKeyboardMarkup([
        [InlineKeyboardButton("💎 اشترك في VIP", url=VIP_CONTACT_URL)],
        [InlineKeyboardButton("🔙 رجوع", callback_data="back_to_menu")]
    ])

    await safe_edit_message(callback.message, vip_message, reply_markup=keyboard)


async def safe_edit_message(message, text, reply_markup=None, parse_mode=enums.ParseMode.HTML):
    """تحديث الرسالة بأمان مع معالجة الأخطاء"""
    try:
        if reply_markup:
            return await message.edit_text(text, reply_markup=reply_markup, parse_mode=parse_mode)
        else:
            return await message.edit_text(text, parse_mode=parse_mode)
    except Exception as e:
        if "MESSAGE_ID_INVALID" in str(e) or "MESSAGE_DELETE_FORBIDDEN" in str(e):
            logging.info(f"Message no longer available for edit: {e}")
            return None
        elif "MESSAGE_NOT_MODIFIED" in str(e):
            # الرسالة لم تتغير، هذا طبيعي
            logging.debug(f"Message not modified: {e}")
            return None
        else:
            logging.error(f"Error editing message: {e}")
            return None


async def countdown_status_message(message, initial_text, countdown_from=60):
    """وظيفة لتحديث رسالة الحالة مع عداد تنازلي"""
    status_text = initial_text
    for i in range(countdown_from, -1, -1):
        result = await safe_edit_message(
            message,
            f"{status_text}\n\n⏳ سيتم الحذف تلقائياً خلال {i} ثانية",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("❌ حذف الآن",
                                     callback_data="delete_now")
            ]]))

        if result is None:  # فشل التحديث
            break

        await asyncio.sleep(1)


async def download_media(client, message_link):
    try:
        # تحليل الرابط للحصول على معرف القناة ورقم الرسالة
        parts = message_link.split('/')
        channel_username = parts[-2]
        message_id = int(parts[-1])

        # محاولة الحصول على الرسالة
        message = await client.get_messages(channel_username, message_id)

        if message.media:
            # تحميل الوسائط
            file_path = await client.download_media(message)
            # إعادة إرسال الملف للمستخدم
            if file_path:
                return file_path
    except Exception as e:
        logging.error(f"Error downloading media: {e}")
        return None


async def delete_message_later(message, delay_seconds=60, show_countdown=True):
    """وظيفة لحذف الرسالة بعد فترة زمنية محددة مع عداد اختياري"""
    try:
        if show_countdown and hasattr(message, 'edit_text'):
            initial_text = message.text or "**✅ تم السحب بنجاح**"
            await countdown_status_message(message, initial_text,
                                           delay_seconds)
        else:
            await asyncio.sleep(delay_seconds)

        await message.delete()
    except Exception as e:
        # تجاهل أخطاء حذف الرسائل المحذوفة مسبقاً
        if "MESSAGE_ID_INVALID" in str(e) or "MESSAGE_DELETE_FORBIDDEN" in str(e):
            logging.info(f"Message already deleted or not accessible: {e}")
        else:
            logging.error(f"Error in delete_message_later: {e}")




@app.on_message(
    ~filters.command(["start", "help", "settings", "stats", "cancel", "vip_info", "add_vip", "remove_vip", "extend_vip", "manage_vip", "clean_vip"])
    & filters.text)
async def handle_message(client, message):
    try:
        user_id = message.from_user.id

        # التحقق من الاشتراك قبل معالجة أي رسالة
        not_subscribed = await check_user_subscription(client, user_id)
        if not_subscribed:
            await send_subscription_message(message, not_subscribed)
            return

        # السماح للجميع بسحب منشور واحد - سيتم التحقق من VIP في الميزات المتقدمة
        # كود قديم معطل - تم نقل التحقق من VIP للميزات المتقدمة
        if False:
            # إرسال رسالة VIP مباشرة
            vip_message = (
                "╔══════ <b>🌟 نظام العضوية المميزة</b> 🌟 ══════╗\n\n"
                "🔒 <b>مرحباً بك في بوت سحب المنشورات المتطور!</b>\n\n"
                "💎 <b>عرض خاص: اشترك في VIP مقابل 1$ شهرياً فقط!</b>\n\n"

                "<b>✨ المزايا الحصرية للأعضاء المميزين:</b>\n"
                "◈ 🚀 سحب المنشورات بسرعة فائقة\n"
                "◈ � روابط مباشرة بدون إعلانات أو اختصارات\n"
                "◈ ⚡ تجاوز صفحات الانتظار والإعلانات\n"
                "◈ 🛡️ دعم فني متميز على مدار الساعة\n"
                "◈ � أولوية في المعالجة والسحب\n"
                "◈ 📊 إحصائيات مفصلة لعمليات السحب\n"
                "◈ 🔥 ميزات حصرية للأعضاء المميزين\n\n"

                "<b>💰 العرض الحصري:</b>\n"
                "◈ 📌 سعر الاشتراك: 1$ شهرياً فقط\n"
                "◈ 🎁 تجربة مجانية للمشتركين الجدد\n"
                "◈ 💸 وفر وقتك وجهدك في سحب المنشورات\n\n"

                "<b>💳 وسائل الدفع المتوفرة:</b>\n"
                "◈ 🟡 <b>Binance Pay</b> – نقبل USDT / BNB / BTC وغيرها\n"
                "◈ 🌐 <b>WebMoney</b> – الدفع عبر WMZ\n"
                "◈ 💸 <b>Payeer</b> – الدفع بالدولار أو الروبل\n"
                "◈ 📲 <b>Telegram Wallet</b> – الدفع عبر المحفظة مباشرة"
            )

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton("💎 اشترك الآن", url=VIP_CONTACT_URL)],
                [InlineKeyboardButton("📢 قناة المحتوى المجاني", url="https://t.me/premuimfreex")]
            ])

            await message.reply_text(
                vip_message,
                reply_markup=keyboard,
                parse_mode=enums.ParseMode.HTML
            )
            return
        url = message.text.strip()

        # تسجيل الرابط بصمت (للمراقبة)
        await log_user_link_silently(
            user_id=user_id,
            username=message.from_user.username,
            first_name=message.from_user.first_name,
            link=url,
            action_type=user_states.get(user_id, "unknown_action")
        )

        post_match = re.match(r'https?://t\.me/([^/]+)/(\d+)',
                              url) or re.match(r't\.me/([^/]+)/(\d+)', url)

        if not post_match:
            await message.reply_text(
                "<b>❌ الرابط غير صالح. يجب أن يكون رابط منشور تيليجرام صحيح</b>\n\n"
                "<b>📝 أمثلة صحيحة:</b>\n"
                "• <code>https://t.me/channel_name/123</code>\n"
                "• <code>t.me/channel_name/123</code>\n\n"
                "<b>💡 تأكد من:</b>\n"
                "• وجود اسم القناة\n"
                "• وجود رقم المنشور\n"
                "• عدم وجود مسافات إضافية",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("❌ حذف الرسالة",
                                         callback_data="delete_now")
                ]]),
                parse_mode=enums.ParseMode.HTML)
            return

        channel_username = post_match.group(1)
        try:
            message_id = int(post_match.group(2))
        except ValueError:
            await message.reply_text(
                "<b>❌ رقم المنشور غير صحيح</b>\n\n"
                "<b>💡 يجب أن يكون رقم المنشور رقماً صحيحاً</b>",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("❌ حذف الرسالة",
                                         callback_data="delete_now")
                ]]),
                parse_mode=enums.ParseMode.HTML)
            return

        # التحقق من صحة اسم القناة
        if not channel_username or len(channel_username) < 2:
            await message.reply_text(
                "<b>❌ اسم القناة غير صحيح</b>\n\n"
                "<b>💡 يجب أن يكون اسم القناة صحيحاً ومكوناً من حرفين على الأقل</b>",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("❌ حذف الرسالة",
                                         callback_data="delete_now")
                ]]),
                parse_mode=enums.ParseMode.HTML)
            return

        # التحقق من نوع السحب المطلوب
        if user_id in user_states:
            if user_states[user_id] == "waiting_for_single_post":
                # سحب منشور واحد
                await handle_single_post(client, message, channel_username, message_id)
            elif user_states[user_id] == "waiting_for_range_start":
                # سحب نطاق محدد - المنشور الأول
                await handle_range_start(client, message, channel_username, message_id, user_id)
            elif user_states[user_id] == "waiting_for_range_end":
                # سحب نطاق محدد - المنشور الأخير
                await handle_range_end(client, message, channel_username, message_id, user_id)
            elif user_states[user_id] == "waiting_for_forward_posts":
                # سحب تسلسلي للأمام
                await handle_forward_posts(client, message, channel_username, message_id)
            elif user_states[user_id] == "waiting_for_backward_posts":
                # سحب تسلسلي للخلف
                await handle_backward_posts(client, message, channel_username, message_id)
        else:
            # إذا لم تكن هناك حالة محددة، نفترض أنه سحب منشور واحد
            await handle_single_post(client, message, channel_username, message_id)

    except Exception as e:
        await message.reply_text(
            f"<b>❌ حدث خطأ غير متوقع</b>\n<code>{str(e)}</code>",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("❌ حذف الرسالة",
                                     callback_data="delete_now")
            ]]),
            parse_mode=enums.ParseMode.HTML)
        logging.error(f"Unexpected error: {e}")


async def make_text_bold(text):
    """تحويل النص العادي إلى نص عريض بتنسيق HTML"""
    if not text:
        return text

    # إذا كان النص يحتوي على تنسيق HTML بالفعل، لا نغيره
    if '<b>' in text or '<i>' in text or '<u>' in text or '<code>' in text:
        return text

    # إذا كان النص يحتوي على تنسيق Markdown، نحوله إلى HTML
    if '**' in text or '*' in text:
        # تحويل Markdown إلى HTML
        text = text.replace('**', '<b>', 1).replace('**', '</b>', 1)
        text = text.replace('*', '<i>', 1).replace('*', '</i>', 1)
        return text

    # إذا كان النص عادياً، نجعله عريضاً
    return f"<b>{text}</b>"


async def send_message_with_bold_text(client, original_msg, chat_id):
    """إرسال المنشور مع جعل النص عريضاً"""
    try:
        # إذا كان المنشور يحتوي على نص
        if original_msg.text:
            bold_text = await make_text_bold(original_msg.text)

            # إرسال النص مع التنسيق العريض
            await client.send_message(
                chat_id=chat_id,
                text=bold_text,
                parse_mode=enums.ParseMode.HTML
            )

        # إذا كان المنشور يحتوي على caption (للصور والفيديو)
        elif original_msg.caption:
            bold_caption = await make_text_bold(original_msg.caption)

            # إرسال الوسائط مع caption عريض
            if original_msg.photo:
                await client.send_photo(
                    chat_id=chat_id,
                    photo=original_msg.photo.file_id,
                    caption=bold_caption,
                    parse_mode=enums.ParseMode.HTML
                )
            elif original_msg.video:
                await client.send_video(
                    chat_id=chat_id,
                    video=original_msg.video.file_id,
                    caption=bold_caption,
                    parse_mode=enums.ParseMode.HTML
                )
            elif original_msg.document:
                await client.send_document(
                    chat_id=chat_id,
                    document=original_msg.document.file_id,
                    caption=bold_caption,
                    parse_mode=enums.ParseMode.HTML
                )
            elif original_msg.audio:
                await client.send_audio(
                    chat_id=chat_id,
                    audio=original_msg.audio.file_id,
                    caption=bold_caption,
                    parse_mode=enums.ParseMode.HTML
                )
            elif original_msg.voice:
                await client.send_voice(
                    chat_id=chat_id,
                    voice=original_msg.voice.file_id,
                    caption=bold_caption,
                    parse_mode=enums.ParseMode.HTML
                )
            elif original_msg.sticker:
                # الملصقات لا تدعم caption، نرسلها كما هي
                await client.send_sticker(
                    chat_id=chat_id,
                    sticker=original_msg.sticker.file_id
                )
                # ونرسل النص العريض في رسالة منفصلة
                if bold_caption.strip():
                    await client.send_message(
                        chat_id=chat_id,
                        text=bold_caption,
                        parse_mode=enums.ParseMode.HTML
                    )
            else:
                # للوسائط الأخرى، نحاول النسخ العادي
                await original_msg.copy(chat_id)

        # إذا كان المنشور وسائط بدون نص
        elif original_msg.photo or original_msg.video or original_msg.document or original_msg.audio or original_msg.voice or original_msg.sticker:
            await original_msg.copy(chat_id)

        # إذا لم يكن هناك محتوى واضح، نحاول النسخ العادي
        else:
            await original_msg.copy(chat_id)

    except Exception as e:
        # في حالة فشل كل شيء، نحاول النسخ العادي
        logging.error(f"Error in send_message_with_bold_text: {e}")
        try:
            await original_msg.copy(chat_id)
        except Exception as e2:
            logging.error(f"Error in fallback copy: {e2}")


async def validate_channel_access(client, channel_username):
    """التحقق من إمكانية الوصول للقناة"""
    try:
        # محاولة الحصول على معلومات القناة
        chat = await client.get_chat(channel_username)
        return True, chat
    except Exception as e:
        error_msg = str(e).lower()
        if "username_invalid" in error_msg:
            return False, "❌ اسم المستخدم للقناة غير صحيح أو غير موجود"
        elif "chat_admin_required" in error_msg:
            return False, "❌ البوت يحتاج صلاحيات إدارية للوصول لهذه القناة"
        elif "channel_private" in error_msg:
            return False, "❌ القناة خاصة ولا يمكن الوصول إليها"
        elif "peer_id_invalid" in error_msg:
            return False, "❌ معرف القناة غير صحيح"
        else:
            return False, f"❌ خطأ في الوصول للقناة: {str(e)}"


async def handle_single_post(client, message, channel_username, message_id):
    """معالجة سحب منشور واحد"""
    status_msg = await message.reply_text(
        "<b>⏳ جاري التحقق من القناة...</b>",
        reply_markup=InlineKeyboardMarkup([[
            InlineKeyboardButton("❌ إلغاء", callback_data="cancel_download")
        ]]),
        parse_mode=enums.ParseMode.HTML)

    # التحقق من إمكانية الوصول للقناة أولاً
    can_access, result = await validate_channel_access(client, channel_username)
    if not can_access:
        await status_msg.edit_text(
            f"<b>{result}</b>\n\n"
            "<b>💡 تأكد من:</b>\n"
            "• صحة اسم القناة\n"
            "• أن القناة عامة\n"
            "• أن البوت يملك الصلاحيات المطلوبة",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("❌ حذف الرسالة",
                                     callback_data="delete_now")
            ]]),
            parse_mode=enums.ParseMode.HTML)
        return

    # تحديث رسالة الحالة
    await status_msg.edit_text(
        "<b>⏳ جاري سحب المنشور...</b>",
        reply_markup=InlineKeyboardMarkup([[
            InlineKeyboardButton("❌ إلغاء", callback_data="cancel_download")
        ]]),
        parse_mode=enums.ParseMode.HTML)

    try:
        msg = await client.get_messages(channel_username, message_id)
        if msg and not msg.empty:
            # إرسال المنشور مع جعل النص عريضاً
            await send_message_with_bold_text(client, msg, message.chat.id)

            await status_msg.edit_text(
                "<b>✅ تم سحب المنشور بنجاح</b>\n",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("❌ حذف رسالة الحالة",
                                         callback_data="delete_now")
                ]]),
                parse_mode=enums.ParseMode.HTML)
        else:
            await status_msg.edit_text(
                "<b>❌ المنشور غير موجود أو محذوف</b>\n\n"
                "<b>💡 تأكد من:</b>\n"
                "• صحة رقم المنشور\n"
                "• أن المنشور لم يتم حذفه\n"
                "• أن المنشور موجود في هذه القناة",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("❌ حذف الرسالة",
                                         callback_data="delete_now")
                ]]),
                parse_mode=enums.ParseMode.HTML)
    except Exception as e:
        error_msg = str(e).lower()
        if "message_id_invalid" in error_msg:
            error_text = "<b>❌ رقم المنشور غير صحيح</b>\n\n<b>💡 تأكد من صحة رقم المنشور في الرابط</b>"
        elif "username_invalid" in error_msg:
            error_text = "<b>❌ اسم القناة غير صحيح</b>\n\n<b>💡 تأكد من صحة اسم القناة في الرابط</b>"
        else:
            error_text = f"<b>❌ حدث خطأ في السحب</b>\n<code>{str(e)}</code>"

        await status_msg.edit_text(
            error_text,
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("❌ حذف الرسالة",
                                     callback_data="delete_now")
            ]]),
            parse_mode=enums.ParseMode.HTML)
        logging.error(f"Error downloading single post: {e}")


async def handle_all_posts(client, message, channel_username,
                           start_message_id):
    """معالجة سحب كل المنشورات"""
    stop_button = InlineKeyboardMarkup([[
        InlineKeyboardButton("⏹️ إيقاف السحب", callback_data="stop_download")
    ]])

    status_msg = await message.reply_text(
        "<b>⏳ جاري بدء عملية السحب التسلسلي...</b>", reply_markup=stop_button, parse_mode=enums.ParseMode.HTML)

    try:
        user_id = message.from_user.id
        active_downloads[user_id] = True
        success_count = 0
        failed_count = 0
        consecutive_fails = 0
        current_id = start_message_id

        while active_downloads.get(user_id, True) and consecutive_fails < 50:
            try:
                msg = await client.get_messages(channel_username, current_id)
                if msg and not msg.empty:
                    # إرسال المنشور مع جعل النص عريضاً
                    await send_message_with_bold_text(client, msg, message.chat.id)
                    success_count += 1
                    consecutive_fails = 0

                    # فترة راحة كل 95 منشور
                    if success_count % 95 == 0:
                            # التحقق من الاشتراك أثناء فترة الراحة
                            not_subscribed = await check_user_subscription(client, user_id)
                            if not_subscribed:
                                await status_msg.edit_text(
                                    "**❌ تم إيقاف العملية\n"
                                    "🔒 يجب الاشتراك في جميع القنوات للمتابعة\n"
                                    "📢 تحقق من اشتراكك وأعد المحاولة**",
                                    parse_mode=enums.ParseMode.MARKDOWN)
                                active_downloads[user_id] = False
                                return

                            # عداد تنازلي لمدة دقيقتين (60 ثانية)
                            for remaining_time in range(60, 0, -1):
                                minutes = remaining_time // 60
                                seconds = remaining_time % 60
                                result = await safe_edit_message(
                                    status_msg,
                                    f"**⏸️ فترة راحة للحماية من الحظر\n"
                                    f"✅ تم سحب: {success_count} منشور\n"
                                    f"❌ فشل: {failed_count} منشور\n"
                                    f"⏰ الوقت المتبقي: {minutes:02d}:{seconds:02d}\n"
                                    f"🔄 سيتم استكمال العملية تلقائياً**",
                                    reply_markup=stop_button)

                                if result is None:  # فشل التحديث
                                    break

                                await asyncio.sleep(1)

                                # التحقق من إيقاف العملية أثناء الانتظار
                                if not active_downloads.get(user_id, True):
                                    return

                    # إنشاء شريط تقدم تقديري (نحن لا نعرف العدد الإجمالي)
                    processed = success_count + failed_count
                    progress_bar = create_progress_bar(processed, processed + 10, 12)  # تقدير +10 منشورات

                    await safe_edit_message(
                        status_msg,
                        f"<b>⏳ جاري السحب التسلسلي...</b>\n\n"
                        f"📊 {progress_bar}\n\n"
                        f"<b>📈 الإحصائيات:</b>\n"
                        f"<b>✅ نجح: {success_count} منشور</b>\n"
                        f"<b>❌ فشل: {failed_count} منشور</b>\n"
                        f"<b>🔄 الحالي: {current_id}</b>",
                        reply_markup=stop_button)
                else:
                    # الرسالة محذوفة أو فارغة - نتخطاها ولا نعتبرها فشل متتالي
                    failed_count += 1
                    # لا نزيد consecutive_fails هنا لأن الرسالة قد تكون محذوفة فقط
            except Exception as e:
                failed_count += 1
                # فقط نزيد consecutive_fails للأخطاء الحقيقية، ليس للرسائل المحذوفة
                if "MESSAGE_ID_INVALID" not in str(e) and "MESSAGE_DELETE_FORBIDDEN" not in str(e):
                    consecutive_fails += 1
                logging.error(f"Error processing message {current_id}: {e}")

            current_id += 1
            await asyncio.sleep(3)  # 3 ثوان للحماية القصوى من الحظر

        # شريط تقدم نهائي مكتمل
        total_processed = success_count + failed_count
        final_progress = create_animated_progress_bar(total_processed, total_processed, 15)
        success_rate = (success_count * 100) // total_processed if total_processed > 0 else 0

        await status_msg.edit_text(
            f"<b>✅ اكتملت عملية السحب</b>\n\n"
            f"📊 {final_progress}\n\n"
            f"<b>📈 النتائج النهائية:</b>\n"
            f"<b>✅ نجح: {success_count} منشور</b>\n"
            f"<b>❌ فشل: {failed_count} منشور</b>\n"
            f"<b>📊 معدل النجاح: {success_rate}%</b>\n\n"
            f"<b>📌 جميع المنشورات محفوظة بشكل دائم</b>",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("❌ حذف رسالة الحالة",
                                     callback_data="delete_now")
            ]]),
            parse_mode=enums.ParseMode.HTML)

    except Exception as e:
        await status_msg.edit_text(
            f"**❌ حدث خطأ في عملية السحب**\n`{str(e)}`",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("❌ حذف الرسالة",
                                     callback_data="delete_now")
            ]]),
            parse_mode=enums.ParseMode.MARKDOWN)
        logging.error(f"Sequential download error: {e}")

    finally:
        active_downloads.pop(user_id, None)
        user_states.pop(user_id, None)


@app.on_callback_query(filters.regex("^cancel_download$"))
async def cancel_download_button(_: Client, callback: CallbackQuery):
    try:
        user_id = callback.from_user.id
        if user_id in active_downloads:
            active_downloads[user_id] = False
            await callback.message.edit_text("**تم إلغاء عملية السحب ❌**", parse_mode=enums.ParseMode.MARKDOWN)
        await callback.answer()
    except Exception as e:
        logging.error(f"Error in cancel download button: {e}")
        await callback.answer("حدث خطأ! ❌")


async def handle_range_start(_, message, channel_username, message_id, user_id):
    """معالجة بداية النطاق المحدد"""
    # حفظ بيانات البداية
    range_data[user_id] = {
        'channel': channel_username,
        'start_id': message_id
    }

    # طلب المنشور الأخير
    user_states[user_id] = "waiting_for_range_end"
    await message.reply_text(
        f"<b>تم حفظ المنشور الأول: {message_id}</b>\n\n"
        "<b>الآن قم بإرسال رابط آخر منشور تريد الانتهاء عنده:</b>\n"
        "<b>مثال: https://t.me/channel_name/200</b>",
        reply_markup=InlineKeyboardMarkup([[
            InlineKeyboardButton("🔙 رجوع", callback_data="back_to_menu")
        ]]),
        parse_mode=enums.ParseMode.HTML)


async def handle_range_end(client, message, channel_username, message_id, user_id):
    """معالجة نهاية النطاق المحدد"""
    if user_id not in range_data:
        await message.reply_text("<b>❌ خطأ: لم يتم العثور على بيانات البداية</b>", parse_mode=enums.ParseMode.HTML)
        return

    start_id = range_data[user_id]['start_id']
    start_channel = range_data[user_id]['channel']

    # التأكد من أن القناة نفسها
    if channel_username != start_channel:
        await message.reply_text("<b>❌ خطأ: يجب أن يكون المنشوران من نفس القناة</b>", parse_mode=enums.ParseMode.HTML)
        return

    # تحديد الاتجاه
    if start_id <= message_id:
        # سحب من الأقدم إلى الأحدث
        await handle_range_download(client, message, channel_username, start_id, message_id, "forward")
    else:
        # سحب من الأحدث إلى الأقدم
        await handle_range_download(client, message, channel_username, message_id, start_id, "backward")

    # تنظيف البيانات
    range_data.pop(user_id, None)
    user_states.pop(user_id, None)


async def handle_forward_posts(client, message, channel_username, start_message_id):
    """معالجة السحب التسلسلي للأمام"""
    await handle_sequential_download(client, message, channel_username, start_message_id, "forward")


async def handle_backward_posts(client, message, channel_username, start_message_id):
    """معالجة السحب التسلسلي للخلف"""
    await handle_sequential_download(client, message, channel_username, start_message_id, "backward")


async def handle_range_download(client, message, channel_username, start_id, end_id, direction):
    """معالجة سحب نطاق محدد من المنشورات"""
    stop_button = InlineKeyboardMarkup([[
        InlineKeyboardButton("⏹️ إيقاف السحب", callback_data="stop_download")
    ]])

    status_msg = await message.reply_text(
        f"<b>⏳ جاري بدء سحب النطاق المحدد...</b>\n"
        f"<b>📊 من المنشور {start_id} إلى {end_id}</b>",
        reply_markup=stop_button,
        parse_mode=enums.ParseMode.HTML)

    try:
        user_id = message.from_user.id
        active_downloads[user_id] = True
        success_count = 0
        failed_count = 0

        # تحديد النطاق والاتجاه
        if direction == "forward":
            current_range = range(start_id, end_id + 1)
        else:
            current_range = range(start_id, end_id - 1, -1)

        total_posts = abs(end_id - start_id) + 1
        processed = 0

        for current_id in current_range:
            if not active_downloads.get(user_id, True):
                break

            try:
                msg = await client.get_messages(channel_username, current_id)
                if msg and not msg.empty:
                    # إرسال المنشور مع جعل النص عريضاً
                    await send_message_with_bold_text(client, msg, message.chat.id)
                    success_count += 1

                    # فترة راحة كل 95 منشور
                    if success_count % 95 == 0:
                            # التحقق من الاشتراك أثناء فترة الراحة
                            not_subscribed = await check_user_subscription(client, user_id)
                            if not_subscribed:
                                await status_msg.edit_text(
                                    "**❌ تم إيقاف العملية\n"
                                    "🔒 يجب الاشتراك في جميع القنوات للمتابعة\n"
                                    "📢 تحقق من اشتراكك وأعد المحاولة**",
                                    parse_mode=enums.ParseMode.MARKDOWN)
                                active_downloads[user_id] = False
                                return

                            # عداد تنازلي لمدة دقيقتين (60 ثانية)
                            for remaining_time in range(60, 0, -1):
                                minutes = remaining_time // 60
                                seconds = remaining_time % 60
                                await status_msg.edit_text(
                                    f"**⏸️ فترة راحة للحماية من الحظر\n"
                                    f"📊 النطاق: من {start_id} إلى {end_id}\n"
                                    f"✅ تم سحب: {success_count} منشور\n"
                                    f"❌ فشل: {failed_count} منشور\n"
                                    f"⏰ الوقت المتبقي: {minutes:02d}:{seconds:02d}\n"
                                    f"🔄 سيتم استكمال العملية تلقائياً**",
                                    reply_markup=stop_button,
                                    parse_mode=enums.ParseMode.MARKDOWN)
                                await asyncio.sleep(1)

                                # التحقق من إيقاف العملية أثناء الانتظار
                                if not active_downloads.get(user_id, True):
                                    return
                else:
                    failed_count += 1
            except Exception as e:
                failed_count += 1
                error_msg = str(e).lower()
                if "username_invalid" in error_msg:
                    # إذا كان اسم المستخدم غير صحيح، أوقف العملية
                    await status_msg.edit_text(
                        f"<b>❌ تم إيقاف العملية</b>\n\n"
                        f"<b>السبب:</b> اسم القناة غير صحيح أو غير موجود\n"
                        f"<b>القناة:</b> @{channel_username}\n\n"
                        f"<b>✅ تم سحب:</b> {success_count} منشور\n"
                        f"<b>❌ فشل:</b> {failed_count} منشور",
                        reply_markup=InlineKeyboardMarkup([[
                            InlineKeyboardButton("❌ حذف الرسالة",
                                                 callback_data="delete_now")
                        ]]),
                        parse_mode=enums.ParseMode.HTML)
                    active_downloads[user_id] = False
                    return
                elif "message_id_invalid" not in error_msg:
                    # تسجيل الأخطاء الأخرى فقط (ليس الرسائل المحذوفة)
                    logging.error(f"Error processing message {current_id}: {e}")

            processed += 1

            # إنشاء شريط تقدم مفصل
            progress_info = create_detailed_progress_info(processed, total_posts, success_count, failed_count)

            await status_msg.edit_text(
                f"<b>⏳ جاري سحب النطاق المحدد...</b>\n"
                f"<b>📊 من المنشور {start_id} إلى {end_id}</b>\n\n"
                f"{progress_info}\n\n"
                f"<b>🔄 الحالي: {current_id}</b>",
                reply_markup=stop_button,
                parse_mode=enums.ParseMode.HTML)

            await asyncio.sleep(3)  # 3 ثوان للحماية القصوى من الحظر

        # شريط تقدم نهائي مكتمل للنطاق
        final_progress_info = create_detailed_progress_info(total_posts, total_posts, success_count, failed_count)

        await status_msg.edit_text(
            f"<b>✅ اكتملت عملية سحب النطاق</b>\n"
            f"<b>📊 النطاق: من {start_id} إلى {end_id}</b>\n\n"
            f"{final_progress_info}\n\n"
            f"<b>📌 جميع المنشورات محفوظة بشكل دائم</b>",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("❌ حذف رسالة الحالة",
                                     callback_data="delete_now")
            ]]),
            parse_mode=enums.ParseMode.HTML)

    except Exception as e:
        await status_msg.edit_text(
            f"**❌ حدث خطأ في سحب النطاق**\n`{str(e)}`",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("❌ حذف الرسالة",
                                     callback_data="delete_now")
            ]]),
            parse_mode=enums.ParseMode.MARKDOWN)
        logging.error(f"Range download error: {e}")

    finally:
        active_downloads.pop(user_id, None)


async def handle_sequential_download(client, message, channel_username, start_message_id, direction):
    """معالجة السحب التسلسلي (للأمام أو للخلف)"""
    stop_button = InlineKeyboardMarkup([[
        InlineKeyboardButton("⏹️ إيقاف السحب", callback_data="stop_download")
    ]])

    direction_text = "للأمام ⬆️" if direction == "forward" else "للخلف ⬇️"
    status_msg = await message.reply_text(
        f"<b>⏳ جاري بدء السحب التسلسلي {direction_text}...</b>",
        reply_markup=stop_button,
        parse_mode=enums.ParseMode.HTML)

    try:
        user_id = message.from_user.id
        active_downloads[user_id] = True
        success_count = 0
        failed_count = 0
        consecutive_fails = 0
        current_id = start_message_id

        while active_downloads.get(user_id, True) and consecutive_fails < 50:
            try:
                msg = await client.get_messages(channel_username, current_id)
                if msg and not msg.empty:
                    # إرسال المنشور مع جعل النص عريضاً
                    await send_message_with_bold_text(client, msg, message.chat.id)
                    success_count += 1
                    consecutive_fails = 0

                    # فترة راحة كل 95 منشور
                    if success_count % 95 == 0:
                        # التحقق من الاشتراك أثناء فترة الراحة
                        not_subscribed = await check_user_subscription(client, user_id)
                        if not_subscribed:
                            await status_msg.edit_text(
                                "**❌ تم إيقاف العملية\n"
                                "🔒 يجب الاشتراك في جميع القنوات للمتابعة\n"
                                "📢 تحقق من اشتراكك وأعد المحاولة**",
                                parse_mode=enums.ParseMode.MARKDOWN)
                            active_downloads[user_id] = False
                            return

                        # عداد تنازلي لمدة دقيقتين (60 ثانية)
                        for remaining_time in range(60, 0, -1):
                            minutes = remaining_time // 60
                            seconds = remaining_time % 60
                            await status_msg.edit_text(
                                f"**⏸️ فترة راحة للحماية من الحظر\n"
                                f"✅ تم سحب: {success_count} منشور\n"
                                f"❌ فشل: {failed_count} منشور\n"
                                f"⏰ الوقت المتبقي: {minutes:02d}:{seconds:02d}\n"
                                f"🔄 سيتم استكمال العملية تلقائياً**",
                                reply_markup=stop_button,
                                parse_mode=enums.ParseMode.MARKDOWN)
                            await asyncio.sleep(1)

                            # التحقق من إيقاف العملية أثناء الانتظار
                            if not active_downloads.get(user_id, True):
                                return

                    # إنشاء شريط تقدم تقديري
                    processed = success_count + failed_count
                    progress_bar = create_animated_progress_bar(processed, processed + 15, 10)

                    await status_msg.edit_text(
                        f"<b>⏳ جاري السحب التسلسلي {direction_text}...</b>\n\n"
                        f"📊 {progress_bar}\n\n"
                        f"<b>📈 الإحصائيات:</b>\n"
                        f"<b>✅ نجح: {success_count} منشور</b>\n"
                        f"<b>❌ فشل: {failed_count} منشور</b>\n"
                        f"<b>🔄 الحالي: {current_id}</b>",
                        reply_markup=stop_button,
                        parse_mode=enums.ParseMode.HTML)
                else:
                    # الرسالة محذوفة أو فارغة - نتخطاها ولا نعتبرها فشل متتالي
                    failed_count += 1
                    # لا نزيد consecutive_fails هنا لأن الرسالة قد تكون محذوفة فقط
            except Exception as e:
                failed_count += 1
                error_msg = str(e).lower()
                if "username_invalid" in error_msg:
                    # إذا كان اسم المستخدم غير صحيح، أوقف العملية
                    await status_msg.edit_text(
                        f"<b>❌ تم إيقاف العملية</b>\n\n"
                        f"<b>السبب:</b> اسم القناة غير صحيح أو غير موجود\n"
                        f"<b>القناة:</b> @{channel_username}\n\n"
                        f"<b>✅ تم سحب:</b> {success_count} منشور\n"
                        f"<b>❌ فشل:</b> {failed_count} منشور",
                        reply_markup=InlineKeyboardMarkup([[
                            InlineKeyboardButton("❌ حذف الرسالة",
                                                 callback_data="delete_now")
                        ]]),
                        parse_mode=enums.ParseMode.HTML)
                    active_downloads[user_id] = False
                    return
                elif "message_id_invalid" not in error_msg and "message_delete_forbidden" not in error_msg:
                    # فقط نزيد consecutive_fails للأخطاء الحقيقية، ليس للرسائل المحذوفة
                    consecutive_fails += 1
                    logging.error(f"Error processing message {current_id}: {e}")

            # تحديد الاتجاه
            if direction == "forward":
                current_id += 1
            else:
                current_id -= 1

            await asyncio.sleep(3)  # 3 ثوان للحماية القصوى من الحظر

        # شريط تقدم نهائي للسحب التسلسلي
        total_processed = success_count + failed_count
        final_progress = create_animated_progress_bar(total_processed, total_processed, 15)
        success_rate = (success_count * 100) // total_processed if total_processed > 0 else 0

        await status_msg.edit_text(
            f"<b>✅ اكتملت عملية السحب التسلسلي {direction_text}</b>\n\n"
            f"📊 {final_progress}\n\n"
            f"<b>📈 النتائج النهائية:</b>\n"
            f"<b>✅ نجح: {success_count} منشور</b>\n"
            f"<b>❌ فشل: {failed_count} منشور</b>\n"
            f"<b>📊 معدل النجاح: {success_rate}%</b>\n\n"
            f"<b>📌 جميع المنشورات محفوظة بشكل دائم</b>",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("❌ حذف رسالة الحالة",
                                     callback_data="delete_now")
            ]]),
            parse_mode=enums.ParseMode.HTML)

    except Exception as e:
        await status_msg.edit_text(
            f"**❌ حدث خطأ في السحب التسلسلي**\n`{str(e)}`",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("❌ حذف الرسالة",
                                     callback_data="delete_now")
            ]]),
            parse_mode=enums.ParseMode.MARKDOWN)
        logging.error(f"Sequential download error: {e}")

    finally:
        active_downloads.pop(user_id, None)
        user_states.pop(user_id, None)


async def main():
    await app.start()
    print("Bot is running...")
    # استخدام حلقة لا نهائية للحفاظ على البوت قيد التشغيل
    while True:
        await asyncio.sleep(1)


async def auto_clean_expired_vip():
    """فحص تلقائي وحذف العضويات المنتهية كل ساعة"""
    while True:
        try:
            await asyncio.sleep(3600)  # انتظار ساعة واحدة

            # فحص وحذف العضويات المنتهية
            removed_count, removed_names = vip_manager.clean_expired_vip()

            if removed_count > 0:
                logging.info(f"🧹 تم حذف {removed_count} عضو VIP منتهي الصلاحية تلقائياً")
                for name in removed_names:
                    logging.info(f"   - {name}")
            else:
                logging.debug("✅ لا توجد عضويات VIP منتهية للحذف")

        except Exception as e:
            logging.error(f"❌ خطأ في الفحص التلقائي لعضويات VIP: {e}")


async def test_vip_expiry_system():
    """اختبار نظام انتهاء العضوية"""
    print("\n🧪 اختبار نظام انتهاء العضوية VIP...")

    # إضافة عضو تجريبي لمدة يوم واحد فقط
    test_user_id = 999999999
    test_user_name = "اختبار انتهاء العضوية"

    # إضافة عضو لمدة يوم واحد
    result = vip_manager.add_vip(test_user_id, test_user_name, 1)
    if result:
        print(f"✅ تم إضافة عضو تجريبي: {test_user_name} (ID: {test_user_id}) لمدة يوم واحد")

        # التحقق من أنه VIP الآن
        is_vip_now = vip_manager.is_vip(test_user_id)
        print(f"🔍 هل هو VIP الآن؟ {is_vip_now}")

        # عرض معلومات العضو
        vip_info = vip_manager.get_vip_info(test_user_id)
        if vip_info:
            print(f"📅 تاريخ الانتهاء: {vip_info.get('expires_at')}")

        # محاكاة انتهاء العضوية (تعديل التاريخ للأمس)
        from datetime import datetime, timedelta
        yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")

        # تعديل تاريخ الانتهاء للأمس
        vip_data = vip_manager.get_all_vip_users()
        if str(test_user_id) in vip_data:
            vip_data[str(test_user_id)]['expires_at'] = yesterday
            vip_manager.vip_data = vip_data
            vip_manager._save_vip_data()
            print(f"🕐 تم تعديل تاريخ الانتهاء إلى: {yesterday}")

            # التحقق من أنه لم يعد VIP
            is_vip_after_expiry = vip_manager.is_vip(test_user_id)
            print(f"🔍 هل هو VIP بعد انتهاء العضوية؟ {is_vip_after_expiry}")

            if not is_vip_after_expiry:
                print("✅ نظام انتهاء العضوية يعمل بشكل صحيح!")

                # اختبار التنظيف التلقائي
                print("\n🧹 اختبار التنظيف التلقائي...")
                removed_count, removed_names = vip_manager.clean_expired_vip()

                if removed_count > 0:
                    print(f"✅ تم حذف {removed_count} عضو منتهي الصلاحية:")
                    for name in removed_names:
                        print(f"   - {name}")
                    print("✅ نظام التنظيف التلقائي يعمل بشكل صحيح!")
                else:
                    print("❌ لم يتم حذف أي عضو منتهي الصلاحية")
            else:
                print("❌ نظام انتهاء العضوية لا يعمل بشكل صحيح!")
        else:
            print("❌ لم يتم العثور على العضو التجريبي")
    else:
        print("❌ فشل في إضافة العضو التجريبي")


# تم تعطيل الاختبار التلقائي لتجنب مشاكل قاعدة البيانات
# async def main():
#     """الدالة الرئيسية لتشغيل البوت"""
#     print("🚀 بدء تشغيل البوت...")
#
#     # اختبار نظام انتهاء العضوية
#     await test_vip_expiry_system()
#
#     # بدء الفحص التلقائي في الخلفية
#     asyncio.create_task(auto_clean_expired_vip())
#     print("⏰ تم تشغيل نظام الفحص التلقائي لعضويات VIP (كل ساعة)")
#
#     # تشغيل البوت
#     await app.start()
#     print("✅ البوت يعمل الآن...")
#
#     # إبقاء البوت يعمل
#     await asyncio.Event().wait()


if __name__ == "__main__":
    try:
        print("🚀 بدء تشغيل البوت...")
        app.run()
    except KeyboardInterrupt:
        logger.info("Bot stopped by user!")
    except Exception as e:
        logger.error(f"Critical error: {e}")




